// 地图配置文件 - 支持天地图多种类型
import L from 'leaflet'
export const MAP_CONFIG = {
  // 天地图API密钥
  TIANDITU_API_KEY: 'e1d48f72da909b553623b56aff2ea140',
  
  // 服务器子域名
  SUBDOMAINS: ['0', '1', '2', '3', '4', '5', '6', '7'],
  
  // 默认地图配置
  DEFAULT_OPTIONS: {
    center: [39.9042, 116.4074], // 北京坐标
    zoom: 13,
    minZoom: 1,
    maxZoom: 18,
    attribution: '&copy; <a href="https://www.tianditu.gov.cn/">天地图</a>'
  }
}

// 天地图图层配置
export const TIANDITU_LAYERS = {
  // 矢量底图
  VEC_W: {
    url: 'https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=',
    name: '矢量底图',
    type: 'base'
  },
  // 矢量注记
  CVA_W: {
    url: 'https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=',
    name: '矢量注记',
    type: 'overlay'
  },
  // 影像底图
  IMG_W: {
    url: 'https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=',
    name: '影像底图',
    type: 'base'
  },
  // 影像注记
  CIA_W: {
    url: 'https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=',
    name: '影像注记',
    type: 'overlay'
  },
  // 地形底图
  TER_W: {
    url: 'https://t{s}.tianditu.gov.cn/ter_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=ter&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=',
    name: '地形底图',
    type: 'base'
  },
  // 地形注记
  CTA_W: {
    url: 'https://t{s}.tianditu.gov.cn/cta_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cta&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=',
    name: '地形注记',
    type: 'overlay'
  }
}

// 基础地图类型配置
export const BASE_MAP_TYPES = {
  vector: {
    id: 'vector',
    name: '矢量地图',
    icon: 'fas fa-map',
    description: '清晰的道路和地名标注',
    baseLayers: ['VEC_W'],
    overlayLayers: [] // 移除默认的路网，让用户可以选择是否开启
  },
  satellite: {
    id: 'satellite',
    name: '卫星地图',
    icon: 'fas fa-satellite',
    description: '真实的卫星影像',
    baseLayers: ['IMG_W'],
    overlayLayers: [] // 移除默认注记，让用户选择是否开启
  },
  terrain: {
    id: 'terrain',
    name: '地形地图',
    icon: 'fas fa-mountain',
    description: '地形起伏和海拔信息',
    baseLayers: ['TER_W'],
    overlayLayers: [] // 移除默认注记，让用户选择是否开启
  }
}

// 可选图层配置
export const OVERLAY_LAYERS = {
  roadNetwork: {
    id: 'roadNetwork',
    name: '路网图层',
    icon: 'fas fa-road',
    description: '显示详细的道路网络和地名标注',
    layers: ['CVA_W'], // 使用矢量注记作为路网图层
    enabled: false
  },
  satelliteLabels: {
    id: 'satelliteLabels',
    name: '卫星标注',
    icon: 'fas fa-map-marker-alt',
    description: '显示卫星地图上的地名标注',
    layers: ['CIA_W'], // 使用影像注记
    enabled: false
  },
  terrainLabels: {
    id: 'terrainLabels',
    name: '地形标注',
    icon: 'fas fa-mountain',
    description: '显示地形地图上的地名标注',
    layers: ['CTA_W'], // 使用地形注记
    enabled: false
  },
  traffic: {
    id: 'traffic',
    name: '交通状况',
    icon: 'fas fa-traffic-light',
    description: '显示实时交通流量和拥堵信息',
    layers: [], // 演示用途，实际可集成百度、高德等交通API
    enabled: false
  }
}

// 保持向后兼容的MAP_TYPES
export const MAP_TYPES = BASE_MAP_TYPES

// 创建天地图图层的工厂函数
export function createTiandituLayer(layerKey, apiKey) {
  const layerConfig = TIANDITU_LAYERS[layerKey]
  if (!layerConfig) {
    throw new Error(`Unknown layer: ${layerKey}`)
  }
  
  const url = layerConfig.url + apiKey
  
  return L.tileLayer(url, {
    subdomains: MAP_CONFIG.SUBDOMAINS,
    attribution: MAP_CONFIG.DEFAULT_OPTIONS.attribution,
    maxZoom: MAP_CONFIG.DEFAULT_OPTIONS.maxZoom,
    name: layerConfig.name
  })
}

// 创建地图类型的图层组
export function createMapTypeGroup(mapTypeId, apiKey) {
  const mapType = MAP_TYPES[mapTypeId]
  if (!mapType) {
    throw new Error(`Unknown map type: ${mapTypeId}`)
  }
  
  const layers = []
  
  // 添加底图图层
  mapType.baseLayers.forEach(layerKey => {
    layers.push(createTiandituLayer(layerKey, apiKey))
  })
  
  // 添加覆盖图层
  mapType.overlayLayers.forEach(layerKey => {
    layers.push(createTiandituLayer(layerKey, apiKey))
  })
  
  return L.layerGroup(layers)
}

// 创建可选图层
export function createOverlayLayer(overlayId, apiKey) {
  const overlay = OVERLAY_LAYERS[overlayId]
  if (!overlay) {
    throw new Error(`Unknown overlay: ${overlayId}`)
  }

  const layers = []
  overlay.layers.forEach(layerKey => {
    layers.push(createTiandituLayer(layerKey, apiKey))
  })

  return L.layerGroup(layers)
}

// 获取所有可用的地图类型
export function getAvailableMapTypes() {
  return Object.values(MAP_TYPES)
}

// 获取所有可选图层
export function getAvailableOverlays() {
  return Object.values(OVERLAY_LAYERS)
}
