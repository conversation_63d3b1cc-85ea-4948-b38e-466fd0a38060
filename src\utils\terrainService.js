/**
 * 地形服务工具
 * 提供地形高程数据查询和缓存功能
 */

import axios from 'axios'

/**
 * 地形服务配置
 */
const TERRAIN_CONFIG = {
  // Open-Elevation API (免费)
  OPEN_ELEVATION: {
    url: 'https://api.open-elevation.com/api/v1/lookup',
    batchSize: 100, // 批量查询最大数量
    timeout: 10000,
    priority: 1
  },

  // 备用服务 - Open Topo Data
  OPEN_TOPO_DATA: {
    url: 'https://api.opentopodata.org/v1/srtm90m',
    batchSize: 100,
    timeout: 10000,
    priority: 2
  },

  // 天地图高程服务 (需要API Key)
  TIANDITU_ELEVATION: {
    url: 'https://api.tianditu.gov.cn/v2/dem',
    batchSize: 50,
    timeout: 8000,
    priority: 3,
    apiKey: null // 需要配置API Key
  },

  // 本地模拟数据 (开发测试用)
  LOCAL_MOCK: {
    enabled: true,
    baseElevation: 50, // 基础海拔
    variation: 100,    // 高程变化范围
    priority: 999
  },

  // 缓存配置
  CACHE: {
    maxSize: 10000, // 最大缓存条目数
    expireTime: 24 * 60 * 60 * 1000 // 24小时过期
  },

  // 重试配置
  RETRY: {
    maxAttempts: 3,
    backoffMultiplier: 1.5,
    initialDelay: 1000
  }
}

/**
 * 地形数据缓存
 */
class TerrainCache {
  constructor() {
    this.cache = new Map()
    this.accessTimes = new Map()
  }
  
  /**
   * 生成缓存键
   */
  generateKey(lat, lng, precision = 4) {
    return `${lat.toFixed(precision)},${lng.toFixed(precision)}`
  }
  
  /**
   * 获取缓存数据
   */
  get(lat, lng) {
    const key = this.generateKey(lat, lng)
    const data = this.cache.get(key)
    
    if (data) {
      // 检查是否过期
      if (Date.now() - data.timestamp > TERRAIN_CONFIG.CACHE.expireTime) {
        this.cache.delete(key)
        this.accessTimes.delete(key)
        return null
      }
      
      // 更新访问时间
      this.accessTimes.set(key, Date.now())
      return data.elevation
    }
    
    return null
  }
  
  /**
   * 设置缓存数据
   */
  set(lat, lng, elevation) {
    const key = this.generateKey(lat, lng)
    
    // 如果缓存已满，删除最久未访问的条目
    if (this.cache.size >= TERRAIN_CONFIG.CACHE.maxSize) {
      this.evictOldest()
    }
    
    this.cache.set(key, {
      elevation,
      timestamp: Date.now()
    })
    this.accessTimes.set(key, Date.now())
  }
  
  /**
   * 删除最久未访问的条目
   */
  evictOldest() {
    let oldestKey = null
    let oldestTime = Date.now()
    
    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey)
      this.accessTimes.delete(oldestKey)
    }
  }
  
  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear()
    this.accessTimes.clear()
  }
  
  /**
   * 获取缓存统计
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: TERRAIN_CONFIG.CACHE.maxSize,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0
    }
  }
}

// 创建全局缓存实例
const terrainCache = new TerrainCache()

/**
 * 地形服务类
 */
export class TerrainService {
  constructor() {
    this.requestQueue = []
    this.isProcessing = false
    this.retryCount = 0
    this.maxRetries = 3
  }
  
  /**
   * 查询单个点的地形高程
   */
  async getElevation(lat, lng) {
    // 先检查缓存
    const cached = terrainCache.get(lat, lng)
    if (cached !== null) {
      return cached
    }
    
    try {
      const result = await this.queryElevationAPI([{ lat, lng }])
      if (result && result.length > 0) {
        const elevation = result[0].elevation
        terrainCache.set(lat, lng, elevation)
        return elevation
      }
    } catch (error) {
      console.warn('地形查询失败:', error)
      return null
    }
    
    return null
  }
  
  /**
   * 批量查询地形高程
   */
  async getBatchElevations(locations) {
    const results = []
    const uncachedLocations = []
    const uncachedIndices = []
    
    // 检查缓存
    locations.forEach((location, index) => {
      const cached = terrainCache.get(location.lat, location.lng)
      if (cached !== null) {
        results[index] = cached
      } else {
        uncachedLocations.push(location)
        uncachedIndices.push(index)
      }
    })
    
    // 查询未缓存的数据
    if (uncachedLocations.length > 0) {
      try {
        const apiResults = await this.queryElevationAPI(uncachedLocations)
        
        apiResults.forEach((result, i) => {
          const originalIndex = uncachedIndices[i]
          const location = uncachedLocations[i]
          
          results[originalIndex] = result.elevation
          terrainCache.set(location.lat, location.lng, result.elevation)
        })
      } catch (error) {
        console.warn('批量地形查询失败:', error)
        // 对于失败的查询，填充null值
        uncachedIndices.forEach(index => {
          results[index] = null
        })
      }
    }
    
    return results
  }
  
  /**
   * 调用地形API
   */
  async queryElevationAPI(locations) {
    // 限制批量查询数量
    const batchSize = TERRAIN_CONFIG.OPEN_ELEVATION.batchSize
    if (locations.length > batchSize) {
      const batches = []
      for (let i = 0; i < locations.length; i += batchSize) {
        batches.push(locations.slice(i, i + batchSize))
      }
      
      const results = []
      for (const batch of batches) {
        const batchResults = await this.queryElevationAPI(batch)
        results.push(...batchResults)
      }
      return results
    }
    
    // 准备请求数据
    const requestData = {
      locations: locations.map(loc => ({
        latitude: loc.lat,
        longitude: loc.lng
      }))
    }
    
    try {
      // 首先尝试 Open-Elevation API
      const response = await axios.post(
        TERRAIN_CONFIG.OPEN_ELEVATION.url,
        requestData,
        {
          timeout: TERRAIN_CONFIG.OPEN_ELEVATION.timeout,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
      
      if (response.data && response.data.results) {
        return response.data.results
      }
    } catch (error) {
      console.warn('Open-Elevation API 失败，尝试备用服务:', error.message)
      
      // 尝试备用服务
      try {
        const backupResults = await this.queryBackupAPI(locations)
        return backupResults
      } catch (backupError) {
        console.error('所有地形服务都失败了:', backupError.message)
        throw new Error('地形数据查询失败')
      }
    }
    
    throw new Error('无效的API响应')
  }
  
  /**
   * 调用备用地形API
   */
  async queryBackupAPI(locations) {
    const locationString = locations
      .map(loc => `${loc.lat},${loc.lng}`)
      .join('|')
    
    const response = await axios.get(
      `${TERRAIN_CONFIG.OPEN_TOPO_DATA.url}?locations=${locationString}`,
      {
        timeout: TERRAIN_CONFIG.OPEN_TOPO_DATA.timeout
      }
    )
    
    if (response.data && response.data.results) {
      return response.data.results.map(result => ({
        latitude: result.location.lat,
        longitude: result.location.lng,
        elevation: result.elevation
      }))
    }
    
    throw new Error('备用API响应无效')
  }
  
  /**
   * 沿路径采样地形高程
   */
  async samplePathElevation(startPoint, endPoint, sampleCount = 10) {
    const samples = []
    
    for (let i = 0; i <= sampleCount; i++) {
      const ratio = i / sampleCount
      const lat = startPoint.lat + (endPoint.lat - startPoint.lat) * ratio
      const lng = startPoint.lng + (endPoint.lng - startPoint.lng) * ratio
      
      samples.push({ lat, lng })
    }
    
    return await this.getBatchElevations(samples)
  }
  
  /**
   * 检查路径是否存在地形碰撞
   */
  async checkPathCollision(startPoint, endPoint, flightAltitude, safetyMargin = 20) {
    const elevations = await this.samplePathElevation(startPoint, endPoint, 20)
    
    const collisions = []
    elevations.forEach((elevation, index) => {
      if (elevation !== null) {
        const requiredAltitude = elevation + safetyMargin
        if (flightAltitude < requiredAltitude) {
          const ratio = index / (elevations.length - 1)
          const lat = startPoint.lat + (endPoint.lat - startPoint.lat) * ratio
          const lng = startPoint.lng + (endPoint.lng - startPoint.lng) * ratio
          
          collisions.push({
            position: { lat, lng },
            terrainHeight: elevation,
            requiredAltitude,
            currentAltitude: flightAltitude,
            deficit: requiredAltitude - flightAltitude
          })
        }
      }
    })
    
    return {
      hasCollision: collisions.length > 0,
      collisions,
      maxTerrainHeight: Math.max(...elevations.filter(e => e !== null)),
      minSafeAltitude: Math.max(...elevations.filter(e => e !== null)) + safetyMargin
    }
  }
  
  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return terrainCache.getStats()
  }
  
  /**
   * 清空缓存
   */
  clearCache() {
    terrainCache.clear()
  }
}

// 创建全局实例
export const terrainService = new TerrainService()

// 导出配置和缓存类供测试使用
export { TERRAIN_CONFIG, TerrainCache }
