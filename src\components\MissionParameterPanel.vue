<template>
  <div class="mission-parameter-panel">
    <div class="panel-header">
      <div class="header-title">
        <i class="fas fa-cog"></i>
        <span>任务参数</span>
      </div>
      <div class="mission-type-badge" :class="missionTypeClass">
        {{ getMissionTypeLabel(currentMission?.type) }}
      </div>
    </div>

    <div class="panel-content" v-if="currentMission">
      <!-- 基础参数 -->
      <div class="parameter-section">
        <h4 class="section-title">基础参数</h4>
        <div class="parameter-grid">
          <div class="parameter-item">
            <label>任务名称</label>
            <input 
              type="text" 
              v-model="missionName"
              @blur="updateMissionName"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>飞行高度 (米)</label>
            <input 
              type="number" 
              v-model.number="baseParameters.altitude"
              @input="updateParameters"
              min="10"
              max="1000"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>飞行速度 (m/s)</label>
            <input 
              type="number" 
              v-model.number="baseParameters.speed"
              @input="updateParameters"
              min="1"
              max="30"
              step="0.1"
              class="parameter-input"
            >
          </div>
        </div>
      </div>

      <!-- Survey 特定参数 -->
      <div v-if="currentMission.type === 'survey'" class="parameter-section">
        <h4 class="section-title">
          <i class="fas fa-th"></i>
          <span>区域测绘参数</span>
        </h4>
        <div class="parameter-grid">
          <div class="parameter-item">
            <label>前向重叠率 (%)</label>
            <input 
              type="number" 
              v-model.number="surveyParameters.overlapFront"
              @input="updateParameters"
              min="50"
              max="90"
              step="5"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>侧向重叠率 (%)</label>
            <input 
              type="number" 
              v-model.number="surveyParameters.overlapSide"
              @input="updateParameters"
              min="30"
              max="80"
              step="5"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>飞行方向 (度)</label>
            <input 
              type="number" 
              v-model.number="surveyParameters.direction"
              @input="updateParameters"
              min="0"
              max="359"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>相机视场角 (度)</label>
            <input 
              type="number" 
              v-model.number="surveyParameters.cameraFOV"
              @input="updateParameters"
              min="30"
              max="120"
              class="parameter-input"
            >
          </div>
        </div>
        
        <div class="parameter-info">
          <div class="info-item">
            <span class="info-label">预计航线间距:</span>
            <span class="info-value">{{ calculateLineSpacing().toFixed(1) }}m</span>
          </div>
          <div class="info-item">
            <span class="info-label">地面分辨率:</span>
            <span class="info-value">{{ calculateGSD().toFixed(2) }}cm/px</span>
          </div>
        </div>
      </div>

      <!-- Corridor 特定参数 -->
      <div v-if="currentMission.type === 'corridor'" class="parameter-section">
        <h4 class="section-title">
          <i class="fas fa-road"></i>
          <span>廊道扫描参数</span>
        </h4>
        <div class="parameter-grid">
          <div class="parameter-item">
            <label>廊道宽度 (米)</label>
            <input 
              type="number" 
              v-model.number="corridorParameters.width"
              @input="updateParameters"
              min="50"
              max="1000"
              step="10"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>航线间距 (米)</label>
            <input 
              type="number" 
              v-model.number="corridorParameters.spacing"
              @input="updateParameters"
              min="20"
              max="200"
              step="5"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>边界缓冲 (米)</label>
            <input 
              type="number" 
              v-model.number="corridorParameters.buffer"
              @input="updateParameters"
              min="0"
              max="50"
              step="5"
              class="parameter-input"
            >
          </div>
        </div>
        
        <div class="parameter-info">
          <div class="info-item">
            <span class="info-label">预计航线数:</span>
            <span class="info-value">{{ Math.ceil(corridorParameters.width / corridorParameters.spacing) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">覆盖宽度:</span>
            <span class="info-value">{{ corridorParameters.width }}m</span>
          </div>
        </div>
      </div>

      <!-- Structure 特定参数 -->
      <div v-if="currentMission.type === 'structure'" class="parameter-section">
        <h4 class="section-title">
          <i class="fas fa-cube"></i>
          <span>结构扫描参数</span>
        </h4>
        <div class="parameter-grid">
          <div class="parameter-item">
            <label>扫描半径 (米)</label>
            <input 
              type="number" 
              v-model.number="structureParameters.radius"
              @input="updateParameters"
              min="20"
              max="200"
              step="5"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>扫描层数</label>
            <input 
              type="number" 
              v-model.number="structureParameters.layers"
              @input="updateParameters"
              min="1"
              max="10"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>每层航点数</label>
            <input 
              type="number" 
              v-model.number="structureParameters.pointsPerLayer"
              @input="updateParameters"
              min="4"
              max="16"
              class="parameter-input"
            >
          </div>
          <div class="parameter-item">
            <label>层间高度差 (米)</label>
            <input 
              type="number" 
              v-model.number="structureParameters.layerHeight"
              @input="updateParameters"
              min="5"
              max="50"
              class="parameter-input"
            >
          </div>
        </div>
        
        <div class="parameter-info">
          <div class="info-item">
            <span class="info-label">总航点数:</span>
            <span class="info-value">{{ structureParameters.layers * structureParameters.pointsPerLayer }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">高度范围:</span>
            <span class="info-value">{{ baseParameters.altitude }}m - {{ baseParameters.altitude + (structureParameters.layers - 1) * structureParameters.layerHeight }}m</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="parameter-actions">
        <button 
          class="action-btn regenerate-btn"
          @click="regeneratePath"
          :disabled="!canRegenerate"
        >
          <i class="fas fa-sync-alt"></i>
          <span>重新生成路径</span>
        </button>
        
        <button 
          class="action-btn reset-btn"
          @click="resetParameters"
        >
          <i class="fas fa-undo"></i>
          <span>重置参数</span>
        </button>
        
        <button 
          class="action-btn save-btn"
          @click="saveAsTemplate"
        >
          <i class="fas fa-save"></i>
          <span>保存为模板</span>
        </button>
      </div>
    </div>

    <div v-else class="empty-panel">
      <div class="empty-icon">
        <i class="fas fa-cog"></i>
      </div>
      <div class="empty-text">请先创建或选择任务</div>
    </div>
  </div>
</template>

<script>
import { missionStore } from '@/stores/missionStore.js'
import { MissionTypes, MissionTypeLabels } from '@/models/MissionTask.js'

export default {
  name: 'MissionParameterPanel',
  setup() {
    return {
      missionStore
    }
  },
  data() {
    return {
      missionName: '',
      baseParameters: {
        altitude: 100,
        speed: 10
      },
      surveyParameters: {
        overlapFront: 75,
        overlapSide: 65,
        direction: 0,
        cameraFOV: 60
      },
      corridorParameters: {
        width: 200,
        spacing: 50,
        buffer: 10
      },
      structureParameters: {
        radius: 50,
        layers: 3,
        pointsPerLayer: 8,
        layerHeight: 20
      }
    }
  },
  computed: {
    currentMission() {
      return missionStore.state.currentMission
    },
    missionTypeClass() {
      if (!this.currentMission) return ''
      return `type-${this.currentMission.type}`
    },
    canRegenerate() {
      return this.currentMission && this.currentMission.type !== MissionTypes.MANUAL
    }
  },
  watch: {
    currentMission: {
      handler(newMission) {
        if (newMission) {
          this.loadMissionParameters(newMission)
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 获取任务类型标签
     */
    getMissionTypeLabel(type) {
      return MissionTypeLabels[type] || '未知类型'
    },

    /**
     * 加载任务参数
     */
    loadMissionParameters(mission) {
      this.missionName = mission.name
      
      const params = mission.parameters || {}
      
      // 基础参数
      this.baseParameters.altitude = params.altitude || 100
      this.baseParameters.speed = params.speed || 10
      
      // Survey参数
      this.surveyParameters.overlapFront = (params.overlapFront || 0.75) * 100
      this.surveyParameters.overlapSide = (params.overlapSide || 0.65) * 100
      this.surveyParameters.direction = params.direction || 0
      this.surveyParameters.cameraFOV = params.cameraFOV || 60
      
      // Corridor参数
      this.corridorParameters.width = params.width || 200
      this.corridorParameters.spacing = params.spacing || 50
      this.corridorParameters.buffer = params.buffer || 10
      
      // Structure参数
      this.structureParameters.radius = params.radius || 50
      this.structureParameters.layers = params.layers || 3
      this.structureParameters.pointsPerLayer = params.pointsPerLayer || 8
      this.structureParameters.layerHeight = params.layerHeight || 20
    },

    /**
     * 更新任务名称
     */
    updateMissionName() {
      if (this.currentMission && this.missionName.trim()) {
        this.currentMission.name = this.missionName.trim()
        this.currentMission.updatedAt = new Date()
      }
    },

    /**
     * 更新参数
     */
    updateParameters() {
      if (!this.currentMission) return

      const parameters = {
        // 基础参数
        altitude: this.baseParameters.altitude,
        speed: this.baseParameters.speed
      }

      // 根据任务类型添加特定参数
      switch (this.currentMission.type) {
        case MissionTypes.SURVEY:
          Object.assign(parameters, {
            overlapFront: this.surveyParameters.overlapFront / 100,
            overlapSide: this.surveyParameters.overlapSide / 100,
            direction: this.surveyParameters.direction,
            cameraFOV: this.surveyParameters.cameraFOV
          })
          break
        case MissionTypes.CORRIDOR:
          Object.assign(parameters, {
            width: this.corridorParameters.width,
            spacing: this.corridorParameters.spacing,
            buffer: this.corridorParameters.buffer
          })
          break
        case MissionTypes.STRUCTURE:
          Object.assign(parameters, {
            radius: this.structureParameters.radius,
            layers: this.structureParameters.layers,
            pointsPerLayer: this.structureParameters.pointsPerLayer,
            layerHeight: this.structureParameters.layerHeight
          })
          break
      }

      this.currentMission.parameters = parameters
      this.currentMission.updatedAt = new Date()
    },

    /**
     * 计算航线间距
     */
    calculateLineSpacing() {
      const altitude = this.baseParameters.altitude
      const fov = this.surveyParameters.cameraFOV * Math.PI / 180
      const overlapSide = this.surveyParameters.overlapSide / 100
      
      const groundWidth = 2 * altitude * Math.tan(fov / 2)
      return groundWidth * (1 - overlapSide)
    },

    /**
     * 计算地面分辨率
     */
    calculateGSD() {
      // 简化计算，实际应根据相机参数
      const altitude = this.baseParameters.altitude
      return altitude * 0.02 // 假设值
    },

    /**
     * 重新生成路径
     */
    regeneratePath() {
      if (!this.canRegenerate) return

      this.updateParameters()
      this.$emit('regenerate-path')
    },

    /**
     * 重置参数
     */
    resetParameters() {
      if (!this.currentMission) return

      if (confirm('确定要重置所有参数吗？')) {
        // 重置为默认值
        this.baseParameters = { altitude: 100, speed: 10 }
        this.surveyParameters = { overlapFront: 75, overlapSide: 65, direction: 0, cameraFOV: 60 }
        this.corridorParameters = { width: 200, spacing: 50, buffer: 10 }
        this.structureParameters = { radius: 50, layers: 3, pointsPerLayer: 8, layerHeight: 20 }
        
        this.updateParameters()
      }
    },

    /**
     * 保存为模板
     */
    saveAsTemplate() {
      if (!this.currentMission) return

      const templateName = prompt('请输入模板名称:', `${this.currentMission.name}_模板`)
      if (templateName) {
        // 这里可以实现保存模板的逻辑
        console.log('保存模板:', templateName, this.currentMission.parameters)
        alert('模板保存成功！')
      }
    }
  }
}
</script>

<style scoped>
.mission-parameter-panel {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: bold;
}

.header-title i {
  color: #00aaff;
  font-size: 16px;
}

.mission-type-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.type-manual { background: rgba(128, 128, 128, 0.3); color: #888; }
.type-survey { background: rgba(0, 170, 255, 0.3); color: #00aaff; }
.type-corridor { background: rgba(255, 102, 0, 0.3); color: #ff6600; }
.type-structure { background: rgba(153, 102, 255, 0.3); color: #9966ff; }

.panel-content {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.parameter-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title i {
  color: #00aaff;
  font-size: 14px;
}

.parameter-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.parameter-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.parameter-item label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  font-weight: 500;
}

.parameter-input {
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  transition: all 0.3s ease;
}

.parameter-input:focus {
  outline: none;
  border-color: rgba(0, 170, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.2);
}

.parameter-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 10px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
}

.info-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 11px;
  font-weight: bold;
}

.parameter-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover:not([disabled]) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.action-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.regenerate-btn:hover:not([disabled]) {
  background: rgba(0, 170, 255, 0.3);
  border-color: rgba(0, 170, 255, 0.5);
  color: #00aaff;
}

.save-btn:hover:not([disabled]) {
  background: rgba(52, 199, 89, 0.3);
  border-color: rgba(52, 199, 89, 0.5);
  color: #34c759;
}

.empty-panel {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
