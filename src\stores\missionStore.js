/**
 * 飞行任务状态管理
 * 使用Vue 3的响应式API管理任务状态
 */

import { reactive, computed, watch } from 'vue'
import { MissionTask, MissionTypes, MissionStatus } from '../models/MissionTask.js'
import { Waypoint } from '../models/Waypoint.js'

// 创建响应式状态
const state = reactive({
  // 当前任务
  currentMission: null,
  
  // 任务列表
  missions: [],
  
  // 选中的航点
  selectedWaypoint: null,
  
  // 编辑模式
  editMode: false,
  
  // 地图交互模式
  mapMode: 'view', // 'view', 'add_waypoint', 'edit_waypoint'
  
  // 显示设置
  showWaypoints: true,
  showPaths: true,
  showTerrainAnalysis: false,
  
  // 任务执行状态
  isExecuting: false,
  executionProgress: 0,
  
  // 地形检测设置
  terrainCheckEnabled: true,
  safetyMargin: 20, // 安全余量（米）
  
  // 最近使用的任务
  recentMissions: [],
  
  // 应用设置
  settings: {
    defaultAltitude: 100,
    defaultSpeed: 10,
    defaultHoverTime: 3,
    autoSave: true,
    showNotifications: true
  }
})

/**
 * 计算属性
 */
const getters = {
  // 当前任务的航点数量
  waypointCount: computed(() => {
    return state.currentMission ? state.currentMission.waypoints.length : 0
  }),
  
  // 当前任务的统计信息
  missionStats: computed(() => {
    return state.currentMission ? state.currentMission.getStatistics() : null
  }),
  
  // 是否有选中的航点
  hasSelectedWaypoint: computed(() => {
    return state.selectedWaypoint !== null
  }),
  
  // 当前任务是否可以执行
  canExecute: computed(() => {
    if (!state.currentMission) return false
    const validation = state.currentMission.validate()
    return validation.isValid && !state.isExecuting
  }),
  
  // 是否有碰撞风险
  hasCollisionRisk: computed(() => {
    if (!state.currentMission) return false
    return state.currentMission.waypoints.some(wp => wp.isCollision)
  }),
  
  // 任务列表（按更新时间排序）
  sortedMissions: computed(() => {
    return [...state.missions].sort((a, b) => 
      new Date(b.updatedAt) - new Date(a.updatedAt)
    )
  })
}

/**
 * 操作方法
 */
const actions = {
  /**
   * 创建新任务
   */
  createMission(options = {}) {
    const mission = new MissionTask({
      name: options.name || `任务 ${state.missions.length + 1}`,
      type: options.type || MissionTypes.MANUAL,
      description: options.description || '',
      parameters: options.parameters || {}
    })
    
    state.missions.push(mission)
    state.currentMission = mission
    
    // 添加到最近使用
    this.addToRecentMissions(mission)
    
    return mission
  },
  
  /**
   * 加载任务
   */
  loadMission(missionId) {
    const mission = state.missions.find(m => m.id === missionId)
    if (mission) {
      state.currentMission = mission
      state.selectedWaypoint = null
      this.addToRecentMissions(mission)
      return mission
    }
    return null
  },
  
  /**
   * 保存当前任务
   */
  saveMission() {
    if (state.currentMission) {
      state.currentMission.updatedAt = new Date()
      this.saveToLocalStorage()
      return true
    }
    return false
  },
  
  /**
   * 删除任务
   */
  deleteMission(missionId) {
    const index = state.missions.findIndex(m => m.id === missionId)
    if (index !== -1) {
      const mission = state.missions[index]
      state.missions.splice(index, 1)
      
      // 如果删除的是当前任务，清空当前任务
      if (state.currentMission && state.currentMission.id === missionId) {
        state.currentMission = null
        state.selectedWaypoint = null
      }
      
      // 从最近使用中移除
      this.removeFromRecentMissions(missionId)
      
      this.saveToLocalStorage()
      return true
    }
    return false
  },
  
  /**
   * 添加航点
   */
  addWaypoint(position, properties = {}) {
    if (!state.currentMission) {
      this.createMission()
    }
    
    const waypointData = {
      position,
      altitude: properties.altitude || state.settings.defaultAltitude,
      speed: properties.speed || state.settings.defaultSpeed,
      hoverTime: properties.hoverTime || state.settings.defaultHoverTime,
      ...properties
    }
    
    const waypoint = state.currentMission.addWaypoint(waypointData)
    
    // 自动保存
    if (state.settings.autoSave) {
      this.saveMission()
    }
    
    return waypoint
  },
  
  /**
   * 更新航点
   */
  updateWaypoint(waypointId, properties) {
    if (state.currentMission) {
      const waypoint = state.currentMission.updateWaypoint(waypointId, properties)
      if (waypoint && state.settings.autoSave) {
        this.saveMission()
      }
      return waypoint
    }
    return null
  },
  
  /**
   * 删除航点
   */
  removeWaypoint(waypointId) {
    if (state.currentMission) {
      const success = state.currentMission.removeWaypoint(waypointId)
      
      // 如果删除的是选中的航点，清空选择
      if (state.selectedWaypoint && state.selectedWaypoint.id === waypointId) {
        state.selectedWaypoint = null
      }
      
      if (success && state.settings.autoSave) {
        this.saveMission()
      }
      
      return success
    }
    return false
  },
  
  /**
   * 移动航点顺序
   */
  moveWaypoint(fromIndex, toIndex) {
    if (state.currentMission) {
      const success = state.currentMission.moveWaypoint(fromIndex, toIndex)
      if (success && state.settings.autoSave) {
        this.saveMission()
      }
      return success
    }
    return false
  },
  
  /**
   * 选择航点
   */
  selectWaypoint(waypoint) {
    state.selectedWaypoint = waypoint
  },
  
  /**
   * 清空选择
   */
  clearSelection() {
    state.selectedWaypoint = null
  },
  
  /**
   * 设置地图模式
   */
  setMapMode(mode) {
    state.mapMode = mode
  },
  
  /**
   * 切换编辑模式
   */
  toggleEditMode() {
    state.editMode = !state.editMode
    if (!state.editMode) {
      state.selectedWaypoint = null
      state.mapMode = 'view'
    }
  },
  
  /**
   * 添加到最近使用
   */
  addToRecentMissions(mission) {
    const index = state.recentMissions.findIndex(m => m.id === mission.id)
    if (index !== -1) {
      state.recentMissions.splice(index, 1)
    }
    state.recentMissions.unshift(mission)
    
    // 限制最近使用的数量
    if (state.recentMissions.length > 10) {
      state.recentMissions = state.recentMissions.slice(0, 10)
    }
  },
  
  /**
   * 从最近使用中移除
   */
  removeFromRecentMissions(missionId) {
    const index = state.recentMissions.findIndex(m => m.id === missionId)
    if (index !== -1) {
      state.recentMissions.splice(index, 1)
    }
  },
  
  /**
   * 保存到本地存储
   */
  saveToLocalStorage() {
    try {
      const data = {
        missions: state.missions.map(m => m.toJSON()),
        recentMissions: state.recentMissions.map(m => ({ id: m.id, name: m.name })),
        settings: state.settings
      }
      localStorage.setItem('drone_missions', JSON.stringify(data))
    } catch (error) {
      console.error('保存到本地存储失败:', error)
    }
  },
  
  /**
   * 从本地存储加载
   */
  loadFromLocalStorage() {
    try {
      const data = localStorage.getItem('drone_missions')
      if (data) {
        const parsed = JSON.parse(data)
        
        // 加载任务
        if (parsed.missions) {
          state.missions = parsed.missions.map(m => MissionTask.fromJSON(m))
        }
        
        // 加载设置
        if (parsed.settings) {
          Object.assign(state.settings, parsed.settings)
        }
        
        // 重建最近使用列表
        if (parsed.recentMissions) {
          state.recentMissions = parsed.recentMissions
            .map(recent => state.missions.find(m => m.id === recent.id))
            .filter(Boolean)
        }
      }
    } catch (error) {
      console.error('从本地存储加载失败:', error)
    }
  },
  
  /**
   * 初始化存储
   */
  initialize() {
    this.loadFromLocalStorage()
    
    // 监听状态变化，自动保存
    watch(
      () => [state.missions, state.settings],
      () => {
        if (state.settings.autoSave) {
          this.saveToLocalStorage()
        }
      },
      { deep: true }
    )
  }
}

// 导出存储实例
export const missionStore = {
  state,
  getters,
  actions
}

// 初始化
missionStore.actions.initialize()
