# 无人机航点编辑器功能需求文档

## 项目概述

基于Vue.js开发的无人机飞行任务规划系统，专注于UI设计和航点任务实现，暂不涉及实际无人机通信功能。

## 核心功能模块

### 1. 基础航点编辑功能

#### 1.1 地图交互
- **地图显示**：集成Leaflet.js地图组件
- **航点添加**：点击地图添加航点标记
- **航点拖拽**：支持拖拽调整航点位置
- **路径显示**：实时显示航点间连接线

#### 1.2 航点管理
- **航点属性编辑**：
  - 坐标位置（经纬度）
  - 飞行高度（默认100米）
  - 飞行速度（m/s）
  - 执行动作（拍照、录像、悬停等）
  - 悬停时间设置
- **航点列表**：显示所有航点信息
- **航点删除**：支持单个航点删除
- **航点重排序**：调整航点执行顺序

#### 1.3 任务统计
- **总航点数量**：实时统计
- **总飞行距离**：基于航点间距离计算
- **预计飞行时间**：根据距离和速度估算
- **任务概览**：显示关键指标

### 2. 三种核心任务类型

#### 2.1 Survey（区域测绘）
- **定义**：对特定区域进行全面覆盖式航测
- **应用场景**：
  - 大面积地形测绘
  - 农田监测
  - 矿区勘探
- **技术要求**：
  - 网格状飞行路径（"之"字形模式）
  - 数据重叠度控制（前向70-80%，侧向60-70%）
  - 覆盖优化策略
- **实现功能**：
  - 多边形区域绘制
  - 自动路径规划算法
  - 重叠率参数设置
  - 飞行高度统一设置

#### 2.2 Corridor Scan（廊道扫描）
- **定义**：针对狭长带状区域的专项扫描
- **应用场景**：
  - 公路、铁路巡检
  - 河道监测
  - 输电线路检查
- **技术要求**：
  - 沿廊道走向的飞行路径
  - 缓冲区范围设置
  - 边界检测方法
- **实现功能**：
  - 线性路径绘制
  - 廊道宽度设置
  - 平行飞行线生成
  - 飞行顺序优化

#### 2.3 Structure Scan（结构扫描）
- **定义**：针对特定结构体的精细化扫描
- **应用场景**：
  - 建筑物检测
  - 桥梁巡检
  - 塔架监测
  - 山体边坡分析
- **技术要求**：
  - 多角度、多方位拍摄
  - 环绕飞行模式
  - 三维重建支持
- **实现功能**：
  - 环绕飞行路径生成
  - 螺旋上升/下降模式
  - 多层次扫描设置
  - 视角优化算法

### 3. 地形碰撞检测功能

#### 3.1 核心安全功能
- **默认飞行高度**：100米
- **实时地形查询**：显示航点位置地形高程
- **碰撞检测**：检测飞行路径与地形冲突
- **安全警告**：红色显示危险路径段

#### 3.2 地形数据集成
- **数据源选择**：
  - Open-Elevation API（免费）
  - Open Topo Data API
  - Google Elevation API
  - Mapbox Terrain API
- **数据缓存**：本地缓存查询结果
- **批量查询**：优化API调用效率

#### 3.3 碰撞检测算法
- **路径采样**：沿航点间路径采样地形高度
- **安全余量**：默认20米安全距离
- **实时检测**：航点变化时自动重新检测
- **颜色编码**：
  - 绿色：安全路径
  - 红色：碰撞风险路径

#### 3.4 地形分析面板
- **碰撞统计**：
  - 总路径段数
  - 安全路径数量
  - 危险路径数量
  - 碰撞点总数
- **路径详情**：每段路径的地形信息
- **高程剖面图**：可视化显示地形轮廓
- **自动修复**：建议安全飞行高度
- **地形报告**：导出详细分析报告

### 4. 用户界面设计

#### 4.1 布局结构
- **地图容器**：主要显示区域
- **侧边控制面板**：功能操作区
- **航点弹窗**：详细参数编辑
- **地形分析面板**：安全检测信息

#### 4.2 交互体验
- **拖拽操作**：直观的航点位置调整
- **实时反馈**：即时显示地形和碰撞信息
- **颜色提示**：通过颜色快速识别风险
- **弹窗编辑**：便捷的参数修改界面

#### 4.3 视觉设计
- **航点标记**：数字编号，颜色状态指示
- **路径线条**：粗细和颜色表示安全状态
- **警告标识**：碰撞点的醒目标记
- **统计面板**：清晰的数据展示

### 5. 数据管理功能

#### 5.1 任务文件操作
- **任务保存**：JSON格式导出
- **任务加载**：支持文件导入
- **任务模板**：预设常用任务类型
- **历史记录**：最近使用的任务

#### 5.2 数据格式
- **航点数据结构**：
  ```json
  {
    "id": "唯一标识",
    "position": [纬度, 经度],
    "altitude": 飞行高度,
    "speed": 飞行速度,
    "action": "执行动作",
    "hoverTime": 悬停时间
  }
  ```
- **任务元数据**：创建时间、统计信息等

## 技术实现方案

### 技术栈选择
- **前端框架**：Vue.js 3
- **地图组件**：Vue-Leaflet
- **UI组件库**：Element Plus / Ant Design Vue
- **地形服务**：Open-Elevation API
- **几何计算**：Turf.js

### 核心算法
- **路径规划算法**：网格覆盖、线性扫描、环绕飞行
- **地形采样算法**：路径插值和高程查询
- **碰撞检测算法**：安全余量计算和风险评估
- **距离计算**：Haversine公式

### 性能优化
- **数据缓存**：地形高程数据本地缓存
- **批量处理**：API请求合并优化
- **异步加载**：非阻塞的地形查询
- **采样控制**：限制采样点数量

## 项目特色

1. **专注UI设计**：不涉及实际无人机通信，专注用户体验
2. **实时安全检测**：地形碰撞检测确保飞行安全
3. **多任务类型支持**：覆盖主要应用场景
4. **直观操作体验**：拖拽式编辑，所见即所得
5. **完整数据管理**：支持任务保存、加载和分享

## 开发优先级

### 第一阶段：基础功能
1. 地图集成和航点编辑
2. 基本的航点管理功能
3. 简单的路径显示

### 第二阶段：任务类型
1. Survey区域测绘功能
2. Corridor廊道扫描功能
3. Structure结构扫描功能

### 第三阶段：安全检测
1. 地形数据集成
2. 碰撞检测算法
3. 安全警告和修复建议

### 第四阶段：优化完善
1. 性能优化
2. 用户体验改进
3. 功能扩展和测试

## 预期成果

一个功能完整、操作直观的无人机航点编辑器，能够：
- 支持多种飞行任务类型的规划
- 提供实时的地形安全检测
- 具备良好的用户交互体验
- 满足专业无人机作业需求

该系统将为无人机操作人员提供强大的任务规划工具，提高作业效率和飞行安全性。
