/**
 * 路径规划算法
 * 实现三种任务类型的自动路径规划
 */

import * as turf from '@turf/turf'
import { 
  calculateDistance, 
  calculateBearing, 
  calculateDestination,
  calculatePolygonArea,
  calculatePolygonCenter
} from './geometryUtils.js'

/**
 * Survey（区域测绘）路径规划
 */
export class SurveyPlanner {
  constructor(options = {}) {
    this.overlapFront = options.overlapFront || 0.75 // 前向重叠率
    this.overlapSide = options.overlapSide || 0.65   // 侧向重叠率
    this.altitude = options.altitude || 100          // 飞行高度
    this.speed = options.speed || 10                 // 飞行速度
    this.cameraFOV = options.cameraFOV || 60         // 相机视场角（度）
    this.direction = options.direction || 0          // 飞行方向（度）
  }

  /**
   * 生成区域测绘路径
   */
  generatePath(polygon) {
    try {
      // 计算多边形边界框
      const bbox = this.calculateBoundingBox(polygon)
      
      // 计算航线间距
      const lineSpacing = this.calculateLineSpacing()
      
      // 生成平行航线
      const lines = this.generateParallelLines(bbox, lineSpacing)
      
      // 裁剪航线到多边形内
      const clippedLines = this.clipLinesToPolygon(lines, polygon)
      
      // 生成航点
      const waypoints = this.generateWaypoints(clippedLines)
      
      // 优化航点顺序
      const optimizedWaypoints = this.optimizeWaypointOrder(waypoints)
      
      return {
        waypoints: optimizedWaypoints,
        metadata: {
          area: calculatePolygonArea(polygon),
          lineSpacing,
          estimatedTime: this.estimateFlightTime(optimizedWaypoints),
          photoCount: this.estimatePhotoCount(optimizedWaypoints)
        }
      }
    } catch (error) {
      console.error('Survey路径规划失败:', error)
      return { waypoints: [], metadata: {} }
    }
  }

  /**
   * 计算边界框
   */
  calculateBoundingBox(polygon) {
    const coords = polygon.map(p => [p.lng || p.longitude, p.lat])
    const turfPolygon = turf.polygon([coords])
    const bbox = turf.bbox(turfPolygon)
    
    return {
      minLng: bbox[0],
      minLat: bbox[1],
      maxLng: bbox[2],
      maxLat: bbox[3]
    }
  }

  /**
   * 计算航线间距
   */
  calculateLineSpacing() {
    // 基于相机视场角和重叠率计算
    const groundWidth = 2 * this.altitude * Math.tan((this.cameraFOV * Math.PI / 180) / 2)
    return groundWidth * (1 - this.overlapSide)
  }

  /**
   * 生成平行航线
   */
  generateParallelLines(bbox, spacing) {
    const lines = []
    const bearing = this.direction
    const perpBearing = (bearing + 90) % 360

    // 计算边界框的中心点
    const centerLat = (bbox.minLat + bbox.maxLat) / 2
    const centerLng = (bbox.minLng + bbox.maxLng) / 2
    const center = { lat: centerLat, lng: centerLng }

    // 计算边界框的对角线长度
    const diagonal = calculateDistance(
      { lat: bbox.minLat, lng: bbox.minLng },
      { lat: bbox.maxLat, lng: bbox.maxLng }
    )

    // 计算需要的航线数量（确保完全覆盖）
    const lineCount = Math.ceil(diagonal / spacing) + 4
    const startOffset = -(lineCount / 2) * spacing

    // 生成航线
    for (let i = 0; i < lineCount; i++) {
      const offset = startOffset + i * spacing

      // 从中心点开始，向垂直方向偏移
      const lineCenter = calculateDestination(center, Math.abs(offset), offset >= 0 ? perpBearing : (perpBearing + 180) % 360)

      // 生成足够长的航线（沿飞行方向）
      const startPoint = calculateDestination(lineCenter, diagonal, bearing + 180)
      const endPoint = calculateDestination(lineCenter, diagonal, bearing)

      lines.push([startPoint, endPoint])
    }

    return lines
  }

  /**
   * 裁剪航线到多边形内
   */
  clipLinesToPolygon(lines, polygon) {
    const clippedLines = []
    const coords = polygon.map(p => [p.lng || p.longitude, p.lat])
    coords.push(coords[0]) // 确保闭合
    const turfPolygon = turf.polygon([coords])
    
    lines.forEach(line => {
      try {
        const turfLine = turf.lineString([
          [line[0].lng, line[0].lat],
          [line[1].lng, line[1].lat]
        ])
        
        const intersection = turf.lineIntersect(turfLine, turfPolygon)
        if (intersection.features.length >= 2) {
          const points = intersection.features.map(f => ({
            lat: f.geometry.coordinates[1],
            lng: f.geometry.coordinates[0]
          }))
          clippedLines.push(points)
        }
      } catch (error) {
        console.warn('航线裁剪失败:', error)
      }
    })
    
    return clippedLines
  }

  /**
   * 生成航点
   */
  generateWaypoints(lines) {
    const waypoints = []
    let waypointId = 1
    
    lines.forEach((line, lineIndex) => {
      const isReverse = lineIndex % 2 === 1 // 之字形模式
      const points = isReverse ? [...line].reverse() : line
      
      points.forEach(point => {
        waypoints.push({
          id: `survey_wp_${waypointId++}`,
          position: [point.lat, point.lng],
          altitude: this.altitude,
          speed: this.speed,
          action: 'photo',
          hoverTime: 1
        })
      })
    })
    
    return waypoints
  }

  /**
   * 优化航点顺序
   */
  optimizeWaypointOrder(waypoints) {
    // 简单的就近优化
    if (waypoints.length <= 2) return waypoints
    
    const optimized = [waypoints[0]]
    const remaining = waypoints.slice(1)
    
    while (remaining.length > 0) {
      const current = optimized[optimized.length - 1]
      let nearestIndex = 0
      let nearestDistance = Infinity
      
      remaining.forEach((waypoint, index) => {
        const distance = calculateDistance(current, { 
          lat: waypoint.position[0], 
          lng: waypoint.position[1] 
        })
        if (distance < nearestDistance) {
          nearestDistance = distance
          nearestIndex = index
        }
      })
      
      optimized.push(remaining.splice(nearestIndex, 1)[0])
    }
    
    return optimized
  }

  /**
   * 估算飞行时间
   */
  estimateFlightTime(waypoints) {
    let totalTime = 0
    
    for (let i = 0; i < waypoints.length - 1; i++) {
      const current = waypoints[i]
      const next = waypoints[i + 1]
      
      const distance = calculateDistance(
        { lat: current.position[0], lng: current.position[1] },
        { lat: next.position[0], lng: next.position[1] }
      )
      
      totalTime += distance / current.speed + current.hoverTime
    }
    
    return totalTime
  }

  /**
   * 估算拍照数量
   */
  estimatePhotoCount(waypoints) {
    return waypoints.filter(wp => wp.action === 'photo').length
  }
}

/**
 * Corridor（廊道扫描）路径规划
 */
export class CorridorPlanner {
  constructor(options = {}) {
    this.width = options.width || 100        // 廊道宽度
    this.altitude = options.altitude || 100  // 飞行高度
    this.speed = options.speed || 10         // 飞行速度
    this.spacing = options.spacing || 50     // 航线间距
  }

  /**
   * 生成廊道扫描路径
   */
  generatePath(centerLine) {
    try {
      // 创建廊道缓冲区
      const corridor = this.createCorridor(centerLine)
      
      // 生成平行航线
      const lines = this.generateParallelLines(centerLine)
      
      // 生成航点
      const waypoints = this.generateWaypoints(lines)
      
      return {
        waypoints,
        corridor,
        metadata: {
          length: this.calculateLineLength(centerLine),
          width: this.width,
          estimatedTime: this.estimateFlightTime(waypoints)
        }
      }
    } catch (error) {
      console.error('Corridor路径规划失败:', error)
      return { waypoints: [], corridor: null, metadata: {} }
    }
  }

  /**
   * 创建廊道缓冲区
   */
  createCorridor(centerLine) {
    try {
      const coords = centerLine.map(p => [p.lng || p.longitude, p.lat])
      const line = turf.lineString(coords)
      return turf.buffer(line, this.width / 2, { units: 'meters' })
    } catch (error) {
      console.error('创建廊道缓冲区失败:', error)
      return null
    }
  }

  /**
   * 生成平行航线
   */
  generateParallelLines(centerLine) {
    const lines = []
    const lineCount = Math.ceil(this.width / this.spacing)
    
    for (let i = 0; i < lineCount; i++) {
      const offset = (i - (lineCount - 1) / 2) * this.spacing
      const offsetLine = this.offsetLine(centerLine, offset)
      lines.push(offsetLine)
    }
    
    return lines
  }

  /**
   * 偏移线条
   */
  offsetLine(line, distance) {
    try {
      const coords = line.map(p => [p.lng || p.longitude, p.lat])
      const turfLine = turf.lineString(coords)

      if (distance === 0) {
        return line // 中心线不需要偏移
      }

      const offsetted = turf.lineOffset(turfLine, distance, { units: 'meters' })

      if (offsetted && offsetted.geometry && offsetted.geometry.coordinates) {
        return offsetted.geometry.coordinates.map(coord => ({
          lat: coord[1],
          lng: coord[0]
        }))
      } else {
        // 如果Turf.js偏移失败，使用简单的几何偏移
        return this.simpleOffsetLine(line, distance)
      }
    } catch (error) {
      console.warn('线条偏移失败，使用简单偏移:', error)
      return this.simpleOffsetLine(line, distance)
    }
  },

  /**
   * 简单的线条偏移算法
   */
  simpleOffsetLine(line, distance) {
    const offsetPoints = []

    for (let i = 0; i < line.length; i++) {
      const current = line[i]
      let bearing

      if (i === 0) {
        // 第一个点，使用到下一个点的方向
        bearing = calculateBearing(current, line[i + 1])
      } else if (i === line.length - 1) {
        // 最后一个点，使用从上一个点的方向
        bearing = calculateBearing(line[i - 1], current)
      } else {
        // 中间点，使用平均方向
        const bearing1 = calculateBearing(line[i - 1], current)
        const bearing2 = calculateBearing(current, line[i + 1])
        bearing = (bearing1 + bearing2) / 2
      }

      // 垂直方向偏移
      const offsetBearing = (bearing + 90) % 360
      const offsetPoint = calculateDestination(current, Math.abs(distance),
        distance > 0 ? offsetBearing : (offsetBearing + 180) % 360)

      offsetPoints.push(offsetPoint)
    }

    return offsetPoints
  }

  /**
   * 生成航点
   */
  generateWaypoints(lines) {
    const waypoints = []
    let waypointId = 1
    
    lines.forEach((line, lineIndex) => {
      const isReverse = lineIndex % 2 === 1
      const points = isReverse ? [...line].reverse() : line
      
      points.forEach(point => {
        waypoints.push({
          id: `corridor_wp_${waypointId++}`,
          position: [point.lat, point.lng],
          altitude: this.altitude,
          speed: this.speed,
          action: 'photo',
          hoverTime: 1
        })
      })
    })
    
    return waypoints
  }

  /**
   * 计算线条长度
   */
  calculateLineLength(line) {
    let length = 0
    for (let i = 0; i < line.length - 1; i++) {
      length += calculateDistance(line[i], line[i + 1])
    }
    return length
  }

  /**
   * 估算飞行时间
   */
  estimateFlightTime(waypoints) {
    let totalTime = 0
    
    for (let i = 0; i < waypoints.length - 1; i++) {
      const current = waypoints[i]
      const next = waypoints[i + 1]
      
      const distance = calculateDistance(
        { lat: current.position[0], lng: current.position[1] },
        { lat: next.position[0], lng: next.position[1] }
      )
      
      totalTime += distance / current.speed + current.hoverTime
    }
    
    return totalTime
  }
}

/**
 * Structure（结构扫描）路径规划
 */
export class StructurePlanner {
  constructor(options = {}) {
    this.radius = options.radius || 50       // 环绕半径
    this.altitude = options.altitude || 100  // 飞行高度
    this.speed = options.speed || 8          // 飞行速度
    this.layers = options.layers || 3        // 扫描层数
    this.pointsPerLayer = options.pointsPerLayer || 8 // 每层航点数
    this.layerHeight = options.layerHeight || 20 // 层间高度差
    this.scanMode = options.scanMode || 'circular' // 扫描模式: circular, spiral, zigzag
    this.startAngle = options.startAngle || 0 // 起始角度
  }

  /**
   * 生成结构扫描路径
   */
  generatePath(center) {
    try {
      let waypoints = []

      switch (this.scanMode) {
        case 'circular':
          waypoints = this.generateCircularPath(center)
          break
        case 'spiral':
          waypoints = this.generateSpiralPath(center)
          break
        case 'zigzag':
          waypoints = this.generateZigzagPath(center)
          break
        default:
          waypoints = this.generateCircularPath(center)
      }

      return {
        waypoints,
        metadata: {
          center,
          radius: this.radius,
          layers: this.layers,
          scanMode: this.scanMode,
          estimatedTime: this.estimateFlightTime(waypoints)
        }
      }
    } catch (error) {
      console.error('Structure路径规划失败:', error)
      return { waypoints: [], metadata: {} }
    }
  },

  /**
   * 生成圆形扫描路径
   */
  generateCircularPath(center) {
    const waypoints = []
    let waypointId = 1

    for (let layer = 0; layer < this.layers; layer++) {
      const layerAltitude = this.altitude + layer * this.layerHeight
      const layerRadius = this.radius + layer * 5 // 每层半径略微增加

      for (let i = 0; i < this.pointsPerLayer; i++) {
        const angle = this.startAngle + (360 / this.pointsPerLayer) * i
        const point = calculateDestination(center, layerRadius, angle)

        waypoints.push({
          id: `structure_wp_${waypointId++}`,
          position: [point.lat, point.lng],
          altitude: layerAltitude,
          speed: this.speed,
          action: 'photo',
          hoverTime: 2
        })
      }
    }

    return waypoints
  },

  /**
   * 生成螺旋扫描路径
   */
  generateSpiralPath(center) {
    const waypoints = []
    let waypointId = 1
    const totalPoints = this.layers * this.pointsPerLayer

    for (let i = 0; i < totalPoints; i++) {
      const progress = i / totalPoints
      const layer = Math.floor(i / this.pointsPerLayer)
      const pointInLayer = i % this.pointsPerLayer

      // 螺旋上升
      const altitude = this.altitude + progress * (this.layers - 1) * this.layerHeight
      const radius = this.radius + progress * this.radius * 0.5
      const angle = this.startAngle + (360 / this.pointsPerLayer) * pointInLayer + layer * 45 // 每层旋转45度

      const point = calculateDestination(center, radius, angle)

      waypoints.push({
        id: `structure_wp_${waypointId++}`,
        position: [point.lat, point.lng],
        altitude: altitude,
        speed: this.speed,
        action: 'photo',
        hoverTime: 1.5
      })
    }

    return waypoints
  },

  /**
   * 生成之字形扫描路径
   */
  generateZigzagPath(center) {
    const waypoints = []
    let waypointId = 1

    for (let layer = 0; layer < this.layers; layer++) {
      const layerAltitude = this.altitude + layer * this.layerHeight
      const layerRadius = this.radius

      // 每层使用不同的起始角度，形成之字形
      const layerStartAngle = this.startAngle + layer * (360 / this.layers)

      for (let i = 0; i < this.pointsPerLayer; i++) {
        let angle

        if (layer % 2 === 0) {
          // 偶数层正向
          angle = layerStartAngle + (360 / this.pointsPerLayer) * i
        } else {
          // 奇数层反向
          angle = layerStartAngle + (360 / this.pointsPerLayer) * (this.pointsPerLayer - 1 - i)
        }

        const point = calculateDestination(center, layerRadius, angle)

        waypoints.push({
          id: `structure_wp_${waypointId++}`,
          position: [point.lat, point.lng],
          altitude: layerAltitude,
          speed: this.speed,
          action: 'photo',
          hoverTime: 2
        })
      }
    }

    return waypoints
  }

  /**
   * 估算飞行时间
   */
  estimateFlightTime(waypoints) {
    let totalTime = 0
    
    for (let i = 0; i < waypoints.length - 1; i++) {
      const current = waypoints[i]
      const next = waypoints[i + 1]
      
      const distance = calculateDistance(
        { lat: current.position[0], lng: current.position[1] },
        { lat: next.position[0], lng: next.position[1] }
      )
      
      totalTime += distance / current.speed + current.hoverTime
    }
    
    return totalTime
  }
}

// 导出规划器实例
export const surveyPlanner = new SurveyPlanner()
export const corridorPlanner = new CorridorPlanner()
export const structurePlanner = new StructurePlanner()
