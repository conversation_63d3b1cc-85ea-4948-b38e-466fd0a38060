<template>
  <div class="mission-type-selector">
    <div class="selector-header">
      <div class="header-title">
        <i class="fas fa-tasks"></i>
        <span>任务类型</span>
      </div>
      <div class="current-type" v-if="currentMission">
        <span class="type-label">当前:</span>
        <span class="type-name">{{ getMissionTypeLabel(currentMission.type) }}</span>
      </div>
    </div>

    <div class="mission-types">
      <div 
        v-for="missionType in missionTypes" 
        :key="missionType.id"
        class="mission-type-card"
        :class="{ 
          active: currentMission?.type === missionType.id,
          disabled: isExecuting 
        }"
        @click="selectMissionType(missionType.id)"
      >
        <div class="type-icon">
          <i :class="missionType.icon"></i>
        </div>
        <div class="type-info">
          <div class="type-title">{{ missionType.name }}</div>
          <div class="type-description">{{ missionType.description }}</div>
          <div class="type-features">
            <span 
              v-for="feature in missionType.features" 
              :key="feature"
              class="feature-tag"
            >
              {{ feature }}
            </span>
          </div>
        </div>
        <div class="type-status">
          <i v-if="currentMission?.type === missionType.id" class="fas fa-check-circle"></i>
          <i v-else class="fas fa-circle"></i>
        </div>
      </div>
    </div>

    <!-- 快速创建按钮 -->
    <div class="quick-actions">
      <button 
        class="quick-btn create-btn"
        @click="createNewMission"
        :disabled="isExecuting"
      >
        <i class="fas fa-plus"></i>
        <span>新建任务</span>
      </button>
      
      <button 
        class="quick-btn template-btn"
        @click="showTemplates"
        :disabled="isExecuting"
      >
        <i class="fas fa-file-alt"></i>
        <span>使用模板</span>
      </button>
    </div>

    <!-- 任务模板选择弹窗 -->
    <div v-if="showTemplateDialog" class="template-overlay" @click="closeTemplates">
      <div class="template-dialog" @click.stop>
        <div class="template-header">
          <h3>选择任务模板</h3>
          <button class="close-btn" @click="closeTemplates">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="template-content">
          <div 
            v-for="template in missionTemplates" 
            :key="template.id"
            class="template-item"
            @click="createFromTemplate(template)"
          >
            <div class="template-icon">
              <i :class="template.icon"></i>
            </div>
            <div class="template-info">
              <div class="template-name">{{ template.name }}</div>
              <div class="template-desc">{{ template.description }}</div>
              <div class="template-params">
                <span class="param-item">{{ template.waypointCount }} 航点</span>
                <span class="param-item">{{ template.estimatedTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { missionStore } from '@/stores/missionStore.js'
import { MissionTypes, MissionTypeLabels } from '@/models/MissionTask.js'

export default {
  name: 'MissionTypeSelector',
  setup() {
    return {
      missionStore
    }
  },
  data() {
    return {
      showTemplateDialog: false,
      missionTypes: [
        {
          id: MissionTypes.MANUAL,
          name: '手动航点',
          description: '手动添加和编辑航点，适合简单的飞行任务',
          icon: 'fas fa-hand-pointer',
          features: ['自由编辑', '灵活路径', '精确控制']
        },
        {
          id: MissionTypes.SURVEY,
          name: '区域测绘',
          description: '对指定区域进行全面覆盖式航测',
          icon: 'fas fa-th',
          features: ['网格飞行', '重叠控制', '自动规划']
        },
        {
          id: MissionTypes.CORRIDOR,
          name: '廊道扫描',
          description: '沿线性路径进行带状区域扫描',
          icon: 'fas fa-road',
          features: ['线性扫描', '宽度设置', '平行航线']
        },
        {
          id: MissionTypes.STRUCTURE,
          name: '结构扫描',
          description: '围绕目标结构进行多角度扫描',
          icon: 'fas fa-cube',
          features: ['环绕飞行', '多层扫描', '螺旋模式']
        }
      ],
      missionTemplates: [
        {
          id: 'survey_small',
          name: '小区域测绘',
          description: '适合1平方公里以内的区域测绘',
          icon: 'fas fa-th',
          type: MissionTypes.SURVEY,
          waypointCount: 24,
          estimatedTime: '15分钟'
        },
        {
          id: 'survey_large',
          name: '大区域测绘',
          description: '适合5平方公里以内的大面积测绘',
          icon: 'fas fa-th-large',
          type: MissionTypes.SURVEY,
          waypointCount: 120,
          estimatedTime: '1小时'
        },
        {
          id: 'corridor_road',
          name: '道路巡检',
          description: '适合公路、铁路等线性基础设施巡检',
          icon: 'fas fa-road',
          type: MissionTypes.CORRIDOR,
          waypointCount: 36,
          estimatedTime: '25分钟'
        },
        {
          id: 'structure_building',
          name: '建筑检测',
          description: '适合建筑物、桥梁等结构体检测',
          icon: 'fas fa-building',
          type: MissionTypes.STRUCTURE,
          waypointCount: 16,
          estimatedTime: '12分钟'
        }
      ]
    }
  },
  computed: {
    currentMission() {
      return missionStore.state.currentMission
    },
    isExecuting() {
      return missionStore.state.isExecuting
    }
  },
  methods: {
    /**
     * 获取任务类型标签
     */
    getMissionTypeLabel(type) {
      return MissionTypeLabels[type] || type
    },

    /**
     * 选择任务类型
     */
    selectMissionType(type) {
      if (this.isExecuting) return

      if (this.currentMission) {
        // 如果已有任务，询问是否切换类型
        if (this.currentMission.type !== type) {
          if (confirm('切换任务类型将清空当前航点，确定继续吗？')) {
            this.createMissionWithType(type)
          }
        }
      } else {
        // 创建新任务
        this.createMissionWithType(type)
      }
    },

    /**
     * 创建指定类型的任务
     */
    createMissionWithType(type) {
      const mission = missionStore.actions.createMission({
        type: type,
        name: `${this.getMissionTypeLabel(type)}任务`
      })

      // 根据任务类型设置地图模式
      this.setMapModeForType(type)
      
      this.$emit('mission-created', mission)
    },

    /**
     * 根据任务类型设置地图模式
     */
    setMapModeForType(type) {
      switch (type) {
        case MissionTypes.MANUAL:
          missionStore.actions.setMapMode('add_waypoint')
          break
        case MissionTypes.SURVEY:
          missionStore.actions.setMapMode('draw_polygon')
          break
        case MissionTypes.CORRIDOR:
          missionStore.actions.setMapMode('draw_line')
          break
        case MissionTypes.STRUCTURE:
          missionStore.actions.setMapMode('select_center')
          break
        default:
          missionStore.actions.setMapMode('view')
      }
    },

    /**
     * 创建新任务
     */
    createNewMission() {
      if (this.isExecuting) return

      // 默认创建手动航点任务
      this.selectMissionType(MissionTypes.MANUAL)
    },

    /**
     * 显示模板选择
     */
    showTemplates() {
      if (this.isExecuting) return
      this.showTemplateDialog = true
    },

    /**
     * 关闭模板选择
     */
    closeTemplates() {
      this.showTemplateDialog = false
    },

    /**
     * 从模板创建任务
     */
    createFromTemplate(template) {
      if (this.isExecuting) return

      // 创建基础任务
      const mission = missionStore.actions.createMission({
        type: template.type,
        name: template.name
      })

      // 这里可以根据模板预设一些参数
      mission.parameters = {
        template: template.id,
        ...this.getTemplateParameters(template)
      }

      this.setMapModeForType(template.type)
      this.closeTemplates()
      
      this.$emit('mission-created', mission)
      this.$emit('template-selected', template)
    },

    /**
     * 获取模板参数
     */
    getTemplateParameters(template) {
      const params = {}
      
      switch (template.type) {
        case MissionTypes.SURVEY:
          params.overlapFront = 0.75
          params.overlapSide = 0.65
          params.altitude = template.id === 'survey_large' ? 150 : 100
          break
        case MissionTypes.CORRIDOR:
          params.width = 200
          params.spacing = 50
          params.altitude = 100
          break
        case MissionTypes.STRUCTURE:
          params.radius = 50
          params.layers = 3
          params.pointsPerLayer = 8
          break
      }
      
      return params
    }
  }
}
</script>

<style scoped>
.mission-type-selector {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: bold;
}

.header-title i {
  color: #00aaff;
  font-size: 16px;
}

.current-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.type-label {
  color: rgba(255, 255, 255, 0.6);
}

.type-name {
  color: #00aaff;
  font-weight: bold;
}

.mission-types {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mission-type-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mission-type-card:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.mission-type-card.active {
  background: rgba(0, 170, 255, 0.15);
  border-color: rgba(0, 170, 255, 0.4);
}

.mission-type-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.type-icon {
  width: 40px;
  height: 40px;
  background: rgba(0, 170, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.type-icon i {
  color: #00aaff;
  font-size: 18px;
}

.type-info {
  flex: 1;
  min-width: 0;
}

.type-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 4px;
}

.type-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.type-features {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.feature-tag {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
}

.type-status {
  flex-shrink: 0;
}

.type-status i {
  color: #00aaff;
  font-size: 16px;
}

.quick-actions {
  display: flex;
  gap: 10px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.quick-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-btn:hover:not([disabled]) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.quick-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.create-btn:hover:not([disabled]) {
  background: rgba(52, 199, 89, 0.3);
  border-color: rgba(52, 199, 89, 0.5);
  color: #34c759;
}

/* 模板选择弹窗 */
.template-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
}

.template-dialog {
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  background: rgba(15, 25, 40, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.template-header h3 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.template-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.template-icon {
  width: 40px;
  height: 40px;
  background: rgba(0, 170, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.template-icon i {
  color: #00aaff;
  font-size: 18px;
}

.template-info {
  flex: 1;
}

.template-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 4px;
}

.template-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin-bottom: 6px;
}

.template-params {
  display: flex;
  gap: 12px;
}

.param-item {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
}

/* 滚动条样式 */
.template-content::-webkit-scrollbar {
  width: 6px;
}

.template-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.template-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.template-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
