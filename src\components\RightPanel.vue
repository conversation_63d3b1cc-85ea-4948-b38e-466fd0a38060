<template>
  <div class="right-panel">
    <!-- 右侧悬浮按钮 -->
    <div class="right-floating-buttons">
      <div
        class="floating-btn"
        :class="{ active: isVideoExpanded }"
        @click="toggleVideo"
      >
        <i class="fas fa-video"></i>
        <span>视频</span>
      </div>
    </div>

    <!-- 悬浮视频面板 -->
    <div v-if="isVideoExpanded" class="floating-video-panel">
      <div class="panel-header">
        <i class="fas fa-video"></i>
        <span class="panel-title">单设备监控视频</span>
        <i class="fas fa-times close-btn" @click="toggleVideo"></i>
      </div>

      <div class="video-controls">
        <div
          v-for="control in videoControls"
          :key="control.name"
          class="video-control-btn"
          :class="{ recording: control.active && control.name === '录制' }"
          :title="control.name"
          @click="handleVideoControl(control)"
        >
          <i :class="control.icon"></i>
        </div>
      </div>

      <div class="video-grid">
        <!-- 主视频区域 -->
        <div class="video-feed main" @click="togglePictureInPicture('main')">
          <div class="video-label">A1</div>
          <div class="video-status">
            <div class="status-light recording"></div>
            <div class="status-light"></div>
          </div>
          <div class="pip-indicator" v-if="pipVideo === 'main'">
            <i class="fas fa-expand-arrows-alt"></i>
          </div>
          <!-- 真实视频流 - 主视频 -->
          <video
            ref="mainVideo"
            autoplay
            muted
            loop
            style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
            :poster="videoPoster"
            @loadstart="onVideoLoadStart(0)"
            @canplay="onVideoCanPlay(0)"
            @error="onVideoError(0)"
            @ended="onVideoEnded"
            @loadeddata="onVideoLoadedData"
          >
            <source :src="videoSources.main.primary" type="video/mp4">
            <source :src="videoSources.main.fallback" type="video/mp4">
            您的浏览器不支持视频播放
          </video>
        </div>

        <!-- 底部小视频区域 -->
        <div class="video-bottom-row">
          <div class="video-feed small" @click="togglePictureInPicture('secondary')">
            <div class="video-label">A2</div>
            <div class="video-status">
              <div class="status-light"></div>
            </div>
            <div class="pip-indicator" v-if="pipVideo === 'secondary'">
              <i class="fas fa-expand-arrows-alt"></i>
            </div>
            <!-- 第二个视频流 -->
            <video
              ref="secondaryVideo"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
              :poster="videoPoster2"
              @loadstart="onVideoLoadStart(1)"
              @canplay="onVideoCanPlay(1)"
              @error="onVideoError(1)"
              @ended="onVideoEnded"
              @loadeddata="onVideoLoadedData"
            >
              <source :src="videoSources.secondary.primary" type="video/mp4">
              <source :src="videoSources.secondary.fallback" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
          </div>

          <div class="video-feed small" @click="togglePictureInPicture('third')">
            <div class="video-label">A3</div>
            <div class="video-status">
              <div class="status-light"></div>
            </div>
            <div class="pip-indicator" v-if="pipVideo === 'third'">
              <i class="fas fa-expand-arrows-alt"></i>
            </div>
            <!-- 第三个视频流 -->
            <video
              ref="thirdVideo"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
              :poster="videoPoster3"
              @loadstart="onVideoLoadStart(2)"
              @canplay="onVideoCanPlay(2)"
              @error="onVideoError(2)"
              @ended="onVideoEnded"
              @loadeddata="onVideoLoadedData"
            >
              <source :src="videoSources.third.primary" type="video/mp4">
              <source :src="videoSources.third.fallback" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
          </div>
        </div>
      </div>
    </div>




  </div>
</template>

<script>
export default {
  name: 'RightPanel',
  data() {
    return {
      isVideoExpanded: false, // 视频面板是否展开
      pipVideo: null, // 当前画中画的视频
      videoControls: [
        { name: '录制', icon: 'fas fa-record-vinyl', active: false },
        { name: '截图', icon: 'fas fa-camera', active: false },
        { name: '全屏', icon: 'fas fa-expand', active: false },
        { name: '设置', icon: 'fas fa-cog', active: false }
      ],
      videoSources: {
        main: {
          primary: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
          fallback: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
        },
        secondary: {
          primary: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
          fallback: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4'
        },
        third: {
          primary: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
          fallback: 'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4'
        }
      },
      videoPoster: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='rgba(255,255,255,0.8)' font-size='16'%3E正在连接视频流...%3C/text%3E%3C/svg%3E",
      videoPoster2: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='rgba(255,255,255,0.8)' font-size='12'%3E红外视频流%3C/text%3E%3C/svg%3E",
      videoPoster3: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='rgba(255,255,255,0.8)' font-size='12'%3E可见光视频%3C/text%3E%3C/svg%3E"
    }
  },
  mounted() {
    // 视频相关初始化
  },
  methods: {
    // 切换视频面板显示/隐藏
    toggleVideo() {
      this.isVideoExpanded = !this.isVideoExpanded
    },
    // 画中画功能
    togglePictureInPicture(videoType) {
      if (this.pipVideo === videoType) {
        // 如果当前视频已经是画中画，则退出画中画
        this.exitPictureInPicture()
      } else {
        // 切换到新的画中画视频
        this.enterPictureInPicture(videoType)
      }
    },
    async enterPictureInPicture(videoType) {
      try {
        let videoElement
        switch(videoType) {
          case 'main':
            videoElement = this.$refs.mainVideo
            break
          case 'secondary':
            videoElement = this.$refs.secondaryVideo
            break
          case 'third':
            videoElement = this.$refs.thirdVideo
            break
        }

        if (videoElement && videoElement.requestPictureInPicture) {
          // 先退出当前的画中画
          if (document.pictureInPictureElement) {
            await document.exitPictureInPicture()
          }

          // 进入新的画中画
          await videoElement.requestPictureInPicture()
          this.pipVideo = videoType

          // 监听画中画退出事件
          videoElement.addEventListener('leavepictureinpicture', () => {
            this.pipVideo = null
          })
        }
      } catch (error) {
        console.error('画中画功能不支持或出错:', error)
      }
    },
    async exitPictureInPicture() {
      try {
        if (document.pictureInPictureElement) {
          await document.exitPictureInPicture()
        }
        this.pipVideo = null
      } catch (error) {
        console.error('退出画中画出错:', error)
      }
    },
    handleVideoControl(control) {
      // 清除其他控制状态
      this.videoControls.forEach(ctrl => {
        if (ctrl !== control) ctrl.active = false
      })

      // 切换当前控制状态
      control.active = !control.active

      switch(control.name) {
        case '录制':
          this.handleRecording(control.active)
          break
        case '截图':
          this.takeScreenshot()
          control.active = false // 截图是瞬时操作
          break
        case '全屏':
          this.toggleFullscreen(control.active)
          break
        case '设置':
          this.showVideoSettings(control.active)
          break
      }
    },
    handleRecording(isRecording) {
      if (isRecording) {
        console.log('开始录制视频')
        // 这里可以添加实际的录制逻辑
      } else {
        console.log('停止录制视频')
      }
    },
    takeScreenshot() {
      console.log('截图已保存')
      // 这里可以添加实际的截图逻辑
    },
    toggleFullscreen(isFullscreen) {
      if (isFullscreen) {
        if (this.$refs.mainVideo && this.$refs.mainVideo.requestFullscreen) {
          this.$refs.mainVideo.requestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
      }
    },
    showVideoSettings(show) {
      if (show) {
        console.log('显示视频设置')
      } else {
        console.log('隐藏视频设置')
      }
    },

    // 视频事件处理
    onVideoLoadStart(index) {
      console.log(`视频 ${index + 1} 开始加载`)
    },
    onVideoCanPlay(index) {
      console.log(`视频 ${index + 1} 可以播放`)
    },
    onVideoError(index) {
      console.log(`视频 ${index + 1} 加载失败，尝试备用源`)
    },
    onVideoEnded() {
      console.log('视频播放结束')
    },
    onVideoLoadedData() {
      console.log('视频数据加载完成')
    }
  }
}
</script>

<style scoped>
/* ================================ 右侧面板 ================================ */
.right-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none;
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

/* 右侧悬浮按钮 */
.right-floating-buttons {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 18px;
  pointer-events: auto;
  z-index: 1002;
  align-items: center;
}

.right-floating-buttons .floating-btn {
  padding: 18px 14px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  font-weight: 600;
  backdrop-filter: blur(15px);
  width: 70px;
  height: 70px;
  justify-content: center;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.right-floating-buttons .floating-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.right-floating-buttons .floating-btn.active {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  color: rgba(255, 255, 255, 0.9);
  box-shadow:
    0 0 20px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: scale(1.02);
}

.right-floating-buttons .floating-btn i {
  font-size: 20px;
  color: inherit;
}

/* 悬浮视频面板 */
.floating-video-panel {
    position: fixed;
    top: 100px;
    right: 110px;
    width: 380px;
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(15px);
    pointer-events: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    animation: slideInRight 0.3s ease;
    z-index: 999;
}

.floating-video-panel .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 18px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px 12px 0 0;
    min-height: 50px;
}

.floating-video-panel .panel-header i:first-child {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.floating-video-panel .panel-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    flex: 1;
    margin-left: 12px;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
}

.floating-video-panel .close-btn {
    cursor: pointer;
    color: rgba(255, 255, 255, 0.6);
    transition: color 0.3s ease;
    font-size: 16px;
}

.floating-video-panel .close-btn:hover {
    color: #ff6b6b;
}

.floating-video-panel .video-controls {
    display: flex;
    gap: 10px;
    /* margin-bottom: 16px; */
    padding: 10px 20px;
}

.video-control-btn {
    width: 36px;
    height: 36px;
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 14px;
    backdrop-filter: blur(15px);
    box-shadow:
        0 2px 10px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.video-control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.video-control-btn.recording {
    background: rgba(255, 107, 71, 0.3);
    border-color: rgba(255, 107, 71, 0.8);
    color: #ff6b47;
    box-shadow:
        0 0 15px rgba(255, 107, 71, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: recording-pulse 1s infinite;
}

@keyframes recording-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.floating-video-panel .video-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 470px;
    padding: 0 20px 20px 20px;
}

.video-feed {
    position: relative;
    background: #1a2332;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.video-feed:hover {
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
}

/* 画中画指示器 */
.pip-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    font-size: 10px;
    z-index: 15;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

.video-feed.main {
    flex: 1;
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

/* 底部小视频行 */
.video-bottom-row {
    display: flex;
    gap: 10px;
    height: 120px;
}

.video-feed.small {
    flex: 1;
    height: 100%;
}

.video-label {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

.video-status {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    z-index: 10;
}

.status-light {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
}

.status-light.recording {
    background: #ff6b47;
    animation: status-blink 1s infinite;
}

@keyframes status-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}



/* 响应式调整 */
@media (max-width: 768px) {
    .floating-video-panel {
        width: 320px;
        right: 95px;
        top: 120px;
    }

    .right-floating-buttons {
        right: 15px;
        gap: 15px;
    }

    .right-floating-buttons .floating-btn {
        width: 65px;
        height: 65px;
        font-size: 11px;
    }

    .right-floating-buttons .floating-btn i {
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .floating-video-panel {
        width: calc(100vw - 100px);
        right: 80px;
        top: 140px;
    }

    .right-floating-buttons {
        right: 10px;
        gap: 12px;
    }

    .right-floating-buttons .floating-btn {
        width: 55px;
        height: 55px;
        font-size: 10px;
    }

    .right-floating-buttons .floating-btn i {
        font-size: 16px;
    }
}

.section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header i {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: bold;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 20px;
}

.info-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(15, 25, 40, 0.8));
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 14px;
    transition: all 0.3s ease;
    min-height: 75px;
    position: relative;
    overflow: hidden;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.8));
    border-radius: 0 2px 2px 0;
}

.info-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(15, 25, 40, 0.9));
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
}

.info-card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding-left: 6px;
}

.info-card-header i {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.info-number {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    font-weight: bold;
    min-width: 20px;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 5px;
    border-radius: 3px;
    text-align: center;
}

.info-label {
    color: #ffffff;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.info-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding-left: 6px;
}

.info-value {
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.info-status {
    color: rgba(255, 255, 255, 0.9);
    font-size: 11px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 3px 6px;
    border-radius: 10px;
    display: inline-block;
    width: fit-content;
}

/* 姿态指示器 */
.attitude-indicator {
    width: 120px;
    height: 120px;
    margin: 20px auto;
    position: relative;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 30%, transparent 70%);
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.attitude-markers {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.attitude-marker {
    position: absolute;
    width: 2px;
    height: 20px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 1px;
}

.attitude-marker:nth-child(1) {
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
}

.attitude-marker:nth-child(2) {
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
}

.attitude-marker:nth-child(3) {
    top: 50%;
    left: 5px;
    transform: translateY(-50%) rotate(90deg);
}

.attitude-marker:nth-child(4) {
    top: 50%;
    right: 5px;
    transform: translateY(-50%) rotate(90deg);
}

.attitude-inner {
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    transition: transform 0.5s ease;
}

/* 姿态数据 */
.attitude-data {
    margin-top: 20px;
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.attitude-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.attitude-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.attitude-item .label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    font-weight: 500;
}

.attitude-item .value {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

/* 任务点网格 */
.mission-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 20px;
}

.mission-point {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 10px;
    border-radius: 6px;
    font-size: 11px;
}

.mission-point-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.mission-point-id {
    color: rgba(255, 255, 255, 0.9);
    font-weight: bold;
    font-size: 10px;
}

.mission-point-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
}

.mission-point-status.completed {
    background: rgba(255, 255, 255, 0.8);
    animation: completed-pulse 2s infinite;
}

.mission-point-status.pending {
    background: #ffd700;
    animation: pending-blink 1s infinite;
}

@keyframes completed-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

@keyframes pending-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.mission-point-name {
    color: #ffffff;
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 11px;
}

.mission-point-details {
    color: rgba(255, 255, 255, 0.8);
    font-size: 10px;
}

/* 控制按钮组 */
.control-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 20px;
}

.control-btn {
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px 12px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: bold;
    min-height: 70px;
    justify-content: center;
    backdrop-filter: blur(15px);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.4);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.control-btn.active {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
}

.control-btn i {
    font-size: 20px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .right-panel {
        width: 360px;
    }

    .video-grid {
        height: 350px;
    }
}

@media (max-width: 1200px) {
    .right-panel {
        width: 340px;
    }

    .video-grid {
        height: 320px;
    }

    .attitude-indicator {
        width: 100px;
        height: 100px;
    }

    .info-card {
        padding: 14px;
        min-height: 80px;
    }

    .info-value {
        font-size: 16px;
    }

    .info-label {
        font-size: 14px;
    }
}
</style>
