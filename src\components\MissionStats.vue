<template>
  <div class="mission-stats">
    <div class="stats-header">
      <div class="header-title">
        <i class="fas fa-chart-line"></i>
        <span>任务统计</span>
      </div>
      <div class="mission-status" :class="missionStatusClass">
        <i :class="missionStatusIcon"></i>
        <span>{{ missionStatusText }}</span>
      </div>
    </div>

    <div class="stats-content" v-if="missionStats">
      <!-- 基础统计 -->
      <div class="stats-section">
        <h4 class="section-title">基础信息</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-map-pin"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">航点数量</div>
              <div class="stat-value">{{ missionStats.waypointCount }}</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-route"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">总距离</div>
              <div class="stat-value">{{ formatDistance(missionStats.totalDistance) }}</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">预计时间</div>
              <div class="stat-value">{{ formatTime(missionStats.estimatedTime) }}</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-tachometer-alt"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">平均速度</div>
              <div class="stat-value">{{ missionStats.averageSpeed.toFixed(1) }} m/s</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 高度信息 -->
      <div class="stats-section">
        <h4 class="section-title">高度信息</h4>
        <div class="altitude-info">
          <div class="altitude-item">
            <span class="altitude-label">最大高度:</span>
            <span class="altitude-value">{{ missionStats.maxAltitude }}m</span>
          </div>
          <div class="altitude-item">
            <span class="altitude-label">最小高度:</span>
            <span class="altitude-value">{{ missionStats.minAltitude }}m</span>
          </div>
          <div class="altitude-item">
            <span class="altitude-label">高度差:</span>
            <span class="altitude-value">{{ (missionStats.maxAltitude - missionStats.minAltitude) }}m</span>
          </div>
        </div>
      </div>

      <!-- 安全检查 -->
      <div class="stats-section">
        <h4 class="section-title">安全检查</h4>
        <div class="safety-info">
          <div class="safety-item" :class="{ warning: missionStats.collisionCount > 0 }">
            <div class="safety-icon">
              <i :class="missionStats.collisionCount > 0 ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle'"></i>
            </div>
            <div class="safety-text">
              <div class="safety-label">碰撞检测</div>
              <div class="safety-value">
                {{ missionStats.collisionCount > 0 ? `${missionStats.collisionCount} 个风险点` : '无风险' }}
              </div>
            </div>
          </div>

          <div class="safety-item" :class="{ warning: !isValidMission }">
            <div class="safety-icon">
              <i :class="isValidMission ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
            </div>
            <div class="safety-text">
              <div class="safety-label">任务验证</div>
              <div class="safety-value">
                {{ isValidMission ? '通过' : '存在问题' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务操作 -->
      <div class="stats-section">
        <h4 class="section-title">任务操作</h4>
        <div class="mission-actions">
          <button 
            class="action-btn validate-btn"
            @click="validateMission"
            :disabled="!currentMission"
          >
            <i class="fas fa-check"></i>
            <span>验证任务</span>
          </button>
          
          <button 
            class="action-btn export-btn"
            @click="exportMission"
            :disabled="!currentMission || !missionStats.waypointCount"
          >
            <i class="fas fa-download"></i>
            <span>导出任务</span>
          </button>
          
          <button 
            class="action-btn execute-btn"
            @click="executeMission"
            :disabled="!canExecute"
          >
            <i class="fas fa-play"></i>
            <span>执行任务</span>
          </button>
        </div>
      </div>
    </div>

    <div v-else class="empty-stats">
      <div class="empty-icon">
        <i class="fas fa-chart-line"></i>
      </div>
      <div class="empty-text">暂无任务数据</div>
      <div class="empty-hint">创建任务后将显示统计信息</div>
    </div>

    <!-- 验证结果弹窗 -->
    <div v-if="showValidationResult" class="validation-overlay" @click="closeValidation">
      <div class="validation-dialog" @click.stop>
        <div class="validation-header">
          <h3>任务验证结果</h3>
          <button class="close-btn" @click="closeValidation">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="validation-content">
          <div v-if="validationResult.isValid" class="validation-success">
            <i class="fas fa-check-circle"></i>
            <span>任务验证通过，可以执行</span>
          </div>
          <div v-else class="validation-errors">
            <div class="error-title">
              <i class="fas fa-exclamation-triangle"></i>
              <span>发现以下问题:</span>
            </div>
            <ul class="error-list">
              <li v-for="error in validationResult.errors" :key="error">{{ error }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { missionStore } from '@/stores/missionStore.js'
import { formatDistance, formatTime } from '@/utils/geometryUtils.js'

export default {
  name: 'MissionStats',
  setup() {
    return {
      missionStore,
      formatDistance,
      formatTime
    }
  },
  data() {
    return {
      showValidationResult: false,
      validationResult: { isValid: true, errors: [] }
    }
  },
  computed: {
    currentMission() {
      return missionStore.state.currentMission
    },
    missionStats() {
      return missionStore.getters.missionStats.value
    },
    canExecute() {
      return missionStore.getters.canExecute.value
    },
    isValidMission() {
      if (!this.currentMission) return false
      const validation = this.currentMission.validate()
      return validation.isValid
    },
    missionStatusClass() {
      if (!this.currentMission) return 'status-none'
      if (this.missionStats?.collisionCount > 0) return 'status-warning'
      if (!this.isValidMission) return 'status-error'
      return 'status-ready'
    },
    missionStatusIcon() {
      if (!this.currentMission) return 'fas fa-minus-circle'
      if (this.missionStats?.collisionCount > 0) return 'fas fa-exclamation-triangle'
      if (!this.isValidMission) return 'fas fa-times-circle'
      return 'fas fa-check-circle'
    },
    missionStatusText() {
      if (!this.currentMission) return '无任务'
      if (this.missionStats?.collisionCount > 0) return '有风险'
      if (!this.isValidMission) return '有错误'
      return '就绪'
    }
  },
  methods: {
    /**
     * 验证任务
     */
    validateMission() {
      if (!this.currentMission) return
      
      this.validationResult = this.currentMission.validate()
      this.showValidationResult = true
    },

    /**
     * 导出任务
     */
    exportMission() {
      if (!this.currentMission) return

      const missionData = this.currentMission.toJSON()
      const dataStr = JSON.stringify(missionData, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      
      const link = document.createElement('a')
      link.href = URL.createObjectURL(dataBlob)
      link.download = `mission_${this.currentMission.name}_${new Date().toISOString().slice(0, 10)}.json`
      link.click()
      
      URL.revokeObjectURL(link.href)
    },

    /**
     * 执行任务
     */
    executeMission() {
      if (!this.canExecute) return

      // 这里可以实现任务执行逻辑
      console.log('开始执行任务:', this.currentMission)
      
      // 模拟任务执行
      alert('任务执行功能演示 - 实际项目中这里会启动无人机执行任务')
    },

    /**
     * 关闭验证结果
     */
    closeValidation() {
      this.showValidationResult = false
    }
  }
}
</script>

<style scoped>
.mission-stats {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: bold;
}

.header-title i {
  color: #00aaff;
  font-size: 16px;
}

.mission-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.status-none {
  background: rgba(128, 128, 128, 0.2);
  color: #888;
}

.status-ready {
  background: rgba(52, 199, 89, 0.2);
  color: #34c759;
}

.status-warning {
  background: rgba(255, 149, 0, 0.2);
  color: #ff9500;
}

.status-error {
  background: rgba(255, 69, 58, 0.2);
  color: #ff453a;
}

.stats-content {
  padding: 20px;
  max-height: 500px;
  overflow-y: auto;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-section:last-child {
  margin-bottom: 0;
}

.section-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.stat-icon {
  width: 32px;
  height: 32px;
  background: rgba(0, 170, 255, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon i {
  color: #00aaff;
  font-size: 14px;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  margin-bottom: 2px;
}

.stat-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: bold;
}

.altitude-info,
.safety-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 12px;
}

.altitude-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.altitude-item:last-child {
  margin-bottom: 0;
}

.altitude-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.altitude-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: bold;
}

.safety-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.safety-item:last-child {
  margin-bottom: 0;
}

.safety-item.warning .safety-icon i {
  color: #ff9500;
}

.safety-item.warning .safety-value {
  color: #ff9500;
}

.safety-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.safety-icon i {
  color: #34c759;
  font-size: 16px;
}

.safety-text {
  flex: 1;
}

.safety-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin-bottom: 2px;
}

.safety-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: bold;
}

.mission-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover:not([disabled]) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.action-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.execute-btn:hover:not([disabled]) {
  background: rgba(52, 199, 89, 0.3);
  border-color: rgba(52, 199, 89, 0.5);
  color: #34c759;
}

.empty-stats {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 12px;
  opacity: 0.8;
}

/* 验证结果弹窗 */
.validation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.validation-dialog {
  width: 400px;
  max-width: 90vw;
  background: rgba(15, 25, 40, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  overflow: hidden;
}

.validation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.validation-header h3 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.validation-content {
  padding: 20px;
}

.validation-success {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #34c759;
  font-size: 14px;
}

.validation-errors .error-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff453a;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 12px;
}

.error-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.error-list li {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-bottom: 6px;
  padding-left: 16px;
  position: relative;
}

.error-list li::before {
  content: '•';
  color: #ff453a;
  position: absolute;
  left: 0;
}

/* 滚动条样式 */
.stats-content::-webkit-scrollbar {
  width: 6px;
}

.stats-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.stats-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.stats-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
