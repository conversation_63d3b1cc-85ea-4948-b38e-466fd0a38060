/**
 * 几何计算工具
 * 提供距离、面积、角度等几何计算功能
 */

import * as turf from '@turf/turf'

/**
 * 地球半径常量（米）
 */
const EARTH_RADIUS = 6371000

/**
 * 角度转弧度
 */
export function degreesToRadians(degrees) {
  return degrees * Math.PI / 180
}

/**
 * 弧度转角度
 */
export function radiansToDegrees(radians) {
  return radians * 180 / Math.PI
}

/**
 * 计算两点之间的距离（使用Haversine公式）
 */
export function calculateDistance(point1, point2) {
  const lat1 = point1.lat || point1[1]
  const lng1 = point1.lng || point1.longitude || point1[0]
  const lat2 = point2.lat || point2[1]
  const lng2 = point2.lng || point2.longitude || point2[0]
  
  const lat1Rad = degreesToRadians(lat1)
  const lat2Rad = degreesToRadians(lat2)
  const deltaLatRad = degreesToRadians(lat2 - lat1)
  const deltaLngRad = degreesToRadians(lng2 - lng1)

  const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
            Math.cos(lat1Rad) * Math.cos(lat2Rad) *
            Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return EARTH_RADIUS * c
}

/**
 * 计算方位角（从point1到point2的方向）
 */
export function calculateBearing(point1, point2) {
  const lat1 = degreesToRadians(point1.lat || point1[1])
  const lat2 = degreesToRadians(point2.lat || point2[1])
  const lng1 = degreesToRadians(point1.lng || point1.longitude || point1[0])
  const lng2 = degreesToRadians(point2.lng || point2.longitude || point2[0])
  
  const deltaLng = lng2 - lng1
  
  const y = Math.sin(deltaLng) * Math.cos(lat2)
  const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLng)
  
  const bearing = Math.atan2(y, x)
  
  // 转换为0-360度
  return (radiansToDegrees(bearing) + 360) % 360
}

/**
 * 根据起点、距离和方位角计算终点
 */
export function calculateDestination(startPoint, distance, bearing) {
  const lat1 = degreesToRadians(startPoint.lat || startPoint[1])
  const lng1 = degreesToRadians(startPoint.lng || startPoint.longitude || startPoint[0])
  const bearingRad = degreesToRadians(bearing)
  
  const lat2 = Math.asin(
    Math.sin(lat1) * Math.cos(distance / EARTH_RADIUS) +
    Math.cos(lat1) * Math.sin(distance / EARTH_RADIUS) * Math.cos(bearingRad)
  )
  
  const lng2 = lng1 + Math.atan2(
    Math.sin(bearingRad) * Math.sin(distance / EARTH_RADIUS) * Math.cos(lat1),
    Math.cos(distance / EARTH_RADIUS) - Math.sin(lat1) * Math.sin(lat2)
  )
  
  return {
    lat: radiansToDegrees(lat2),
    lng: radiansToDegrees(lng2)
  }
}

/**
 * 计算多边形面积
 */
export function calculatePolygonArea(coordinates) {
  try {
    // 确保坐标格式正确 [lng, lat]
    const formattedCoords = coordinates.map(coord => {
      if (Array.isArray(coord)) {
        return coord.length === 2 ? coord : [coord[1], coord[0]]
      }
      return [coord.lng || coord.longitude, coord.lat]
    })
    
    // 确保多边形闭合
    if (formattedCoords[0][0] !== formattedCoords[formattedCoords.length - 1][0] ||
        formattedCoords[0][1] !== formattedCoords[formattedCoords.length - 1][1]) {
      formattedCoords.push(formattedCoords[0])
    }
    
    const polygon = turf.polygon([formattedCoords])
    return turf.area(polygon)
  } catch (error) {
    console.error('计算多边形面积失败:', error)
    return 0
  }
}

/**
 * 计算路径总长度
 */
export function calculatePathLength(waypoints) {
  if (waypoints.length < 2) return 0
  
  let totalDistance = 0
  for (let i = 0; i < waypoints.length - 1; i++) {
    totalDistance += calculateDistance(waypoints[i], waypoints[i + 1])
  }
  
  return totalDistance
}

/**
 * 在两点之间插值生成中间点
 */
export function interpolatePoints(point1, point2, count) {
  const points = []
  
  for (let i = 0; i <= count; i++) {
    const ratio = i / count
    const lat = point1.lat + (point2.lat - point1.lat) * ratio
    const lng = (point1.lng || point1.longitude) + 
                ((point2.lng || point2.longitude) - (point1.lng || point1.longitude)) * ratio
    
    points.push({ lat, lng })
  }
  
  return points
}

/**
 * 计算点到线段的最短距离
 */
export function pointToLineDistance(point, lineStart, lineEnd) {
  try {
    const pointFeature = turf.point([point.lng || point.longitude, point.lat])
    const lineFeature = turf.lineString([
      [lineStart.lng || lineStart.longitude, lineStart.lat],
      [lineEnd.lng || lineEnd.longitude, lineEnd.lat]
    ])
    
    return turf.pointToLineDistance(pointFeature, lineFeature, { units: 'meters' })
  } catch (error) {
    console.error('计算点到线距离失败:', error)
    return 0
  }
}

/**
 * 判断点是否在多边形内
 */
export function isPointInPolygon(point, polygon) {
  try {
    const pointFeature = turf.point([point.lng || point.longitude, point.lat])
    
    // 确保多边形坐标格式正确
    const formattedCoords = polygon.map(coord => {
      if (Array.isArray(coord)) {
        return coord.length === 2 ? coord : [coord[1], coord[0]]
      }
      return [coord.lng || coord.longitude, coord.lat]
    })
    
    // 确保多边形闭合
    if (formattedCoords[0][0] !== formattedCoords[formattedCoords.length - 1][0] ||
        formattedCoords[0][1] !== formattedCoords[formattedCoords.length - 1][1]) {
      formattedCoords.push(formattedCoords[0])
    }
    
    const polygonFeature = turf.polygon([formattedCoords])
    return turf.booleanPointInPolygon(pointFeature, polygonFeature)
  } catch (error) {
    console.error('判断点是否在多边形内失败:', error)
    return false
  }
}

/**
 * 创建缓冲区
 */
export function createBuffer(geometry, distance, units = 'meters') {
  try {
    let feature
    
    if (Array.isArray(geometry)) {
      // 如果是点数组，创建线或多边形
      if (geometry.length === 1) {
        feature = turf.point([geometry[0].lng || geometry[0].longitude, geometry[0].lat])
      } else if (geometry.length === 2) {
        feature = turf.lineString([
          [geometry[0].lng || geometry[0].longitude, geometry[0].lat],
          [geometry[1].lng || geometry[1].longitude, geometry[1].lat]
        ])
      } else {
        const coords = geometry.map(p => [p.lng || p.longitude, p.lat])
        if (coords[0][0] !== coords[coords.length - 1][0] || 
            coords[0][1] !== coords[coords.length - 1][1]) {
          coords.push(coords[0])
        }
        feature = turf.polygon([coords])
      }
    } else {
      feature = geometry
    }
    
    return turf.buffer(feature, distance, { units })
  } catch (error) {
    console.error('创建缓冲区失败:', error)
    return null
  }
}

/**
 * 简化路径（减少点数）
 */
export function simplifyPath(waypoints, tolerance = 10) {
  try {
    if (waypoints.length < 3) return waypoints
    
    const coords = waypoints.map(wp => [
      wp.lng || wp.longitude || wp.position[1],
      wp.lat || wp.position[0]
    ])
    
    const line = turf.lineString(coords)
    const simplified = turf.simplify(line, { tolerance: tolerance / 1000, highQuality: true })
    
    return simplified.geometry.coordinates.map(coord => ({
      lat: coord[1],
      lng: coord[0]
    }))
  } catch (error) {
    console.error('简化路径失败:', error)
    return waypoints
  }
}

/**
 * 计算多边形的中心点
 */
export function calculatePolygonCenter(coordinates) {
  try {
    const formattedCoords = coordinates.map(coord => {
      if (Array.isArray(coord)) {
        return coord.length === 2 ? coord : [coord[1], coord[0]]
      }
      return [coord.lng || coord.longitude, coord.lat]
    })
    
    if (formattedCoords[0][0] !== formattedCoords[formattedCoords.length - 1][0] ||
        formattedCoords[0][1] !== formattedCoords[formattedCoords.length - 1][1]) {
      formattedCoords.push(formattedCoords[0])
    }
    
    const polygon = turf.polygon([formattedCoords])
    const center = turf.centroid(polygon)
    
    return {
      lat: center.geometry.coordinates[1],
      lng: center.geometry.coordinates[0]
    }
  } catch (error) {
    console.error('计算多边形中心失败:', error)
    return null
  }
}

/**
 * 格式化距离显示
 */
export function formatDistance(meters) {
  if (meters < 1000) {
    return `${Math.round(meters)}m`
  } else {
    return `${(meters / 1000).toFixed(2)}km`
  }
}

/**
 * 格式化面积显示
 */
export function formatArea(squareMeters) {
  if (squareMeters < 10000) {
    return `${Math.round(squareMeters)}m²`
  } else if (squareMeters < 1000000) {
    return `${(squareMeters / 10000).toFixed(2)}公顷`
  } else {
    return `${(squareMeters / 1000000).toFixed(2)}km²`
  }
}

/**
 * 格式化时间显示
 */
export function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.round(seconds % 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}
