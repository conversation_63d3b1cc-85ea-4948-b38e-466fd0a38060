<template>
  <div class="waypoint-manager">
    <div class="manager-header">
      <div class="header-title">
        <i class="fas fa-map-pin"></i>
        <span>航点管理</span>
      </div>
      <div class="header-actions">
        <button 
          class="action-btn add-btn" 
          @click="toggleAddMode"
          :class="{ active: missionStore.state.mapMode === 'add_waypoint' }"
          title="添加航点"
        >
          <i class="fas fa-plus"></i>
        </button>
        <button 
          class="action-btn clear-btn" 
          @click="clearAllWaypoints"
          :disabled="!waypoints.length"
          title="清除所有"
        >
          <i class="fas fa-trash-alt"></i>
        </button>
      </div>
    </div>

    <div class="waypoint-list" v-if="waypoints.length">
      <div 
        v-for="(waypoint, index) in waypoints" 
        :key="waypoint.id"
        class="waypoint-item"
        :class="{ 
          selected: selectedWaypoint?.id === waypoint.id,
          collision: waypoint.isCollision 
        }"
        @click="selectWaypoint(waypoint)"
      >
        <div class="waypoint-number">{{ index + 1 }}</div>
        
        <div class="waypoint-info">
          <div class="waypoint-coords">
            <span class="coord-label">纬度:</span>
            <span class="coord-value">{{ waypoint.latitude.toFixed(6) }}</span>
          </div>
          <div class="waypoint-coords">
            <span class="coord-label">经度:</span>
            <span class="coord-value">{{ waypoint.longitude.toFixed(6) }}</span>
          </div>
          <div class="waypoint-params">
            <span class="param-item">
              <i class="fas fa-arrows-alt-v"></i>
              {{ waypoint.altitude }}m
            </span>
            <span class="param-item">
              <i class="fas fa-tachometer-alt"></i>
              {{ waypoint.speed }}m/s
            </span>
            <span class="param-item">
              <i class="fas fa-clock"></i>
              {{ waypoint.hoverTime }}s
            </span>
          </div>
        </div>

        <div class="waypoint-actions">
          <button 
            class="waypoint-action-btn edit-btn" 
            @click.stop="editWaypoint(waypoint)"
            title="编辑"
          >
            <i class="fas fa-edit"></i>
          </button>
          <button 
            class="waypoint-action-btn delete-btn" 
            @click.stop="deleteWaypoint(waypoint.id)"
            title="删除"
          >
            <i class="fas fa-trash"></i>
          </button>
          <button 
            class="waypoint-action-btn move-btn" 
            @click.stop="startDrag(waypoint, index)"
            title="移动"
          >
            <i class="fas fa-arrows-alt"></i>
          </button>
        </div>

        <!-- 碰撞警告 -->
        <div v-if="waypoint.isCollision" class="collision-warning">
          <i class="fas fa-exclamation-triangle"></i>
          <span>地形碰撞风险</span>
        </div>
      </div>
    </div>

    <div v-else class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-map-pin"></i>
      </div>
      <div class="empty-text">暂无航点</div>
      <div class="empty-hint">点击地图或使用添加按钮创建航点</div>
    </div>

    <!-- 统计信息 -->
    <div class="waypoint-stats" v-if="waypoints.length">
      <div class="stat-item">
        <span class="stat-label">航点数量:</span>
        <span class="stat-value">{{ waypoints.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">总距离:</span>
        <span class="stat-value">{{ formatDistance(missionStats?.totalDistance || 0) }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">预计时间:</span>
        <span class="stat-value">{{ formatTime(missionStats?.estimatedTime || 0) }}</span>
      </div>
      <div class="stat-item" v-if="missionStats?.collisionCount > 0">
        <span class="stat-label collision">碰撞风险:</span>
        <span class="stat-value collision">{{ missionStats.collisionCount }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { missionStore } from '@/stores/missionStore.js'
import { formatDistance, formatTime } from '@/utils/geometryUtils.js'

export default {
  name: 'WaypointManager',
  setup() {
    return {
      missionStore,
      formatDistance,
      formatTime
    }
  },
  computed: {
    waypoints() {
      return missionStore.state.currentMission?.waypoints || []
    },
    selectedWaypoint() {
      return missionStore.state.selectedWaypoint
    },
    missionStats() {
      return missionStore.getters.missionStats.value
    }
  },
  methods: {
    /**
     * 切换添加模式
     */
    toggleAddMode() {
      if (missionStore.state.mapMode === 'add_waypoint') {
        missionStore.actions.setMapMode('view')
      } else {
        missionStore.actions.setMapMode('add_waypoint')
      }
    },

    /**
     * 选择航点
     */
    selectWaypoint(waypoint) {
      missionStore.actions.selectWaypoint(waypoint)
    },

    /**
     * 编辑航点
     */
    editWaypoint(waypoint) {
      this.selectWaypoint(waypoint)
      this.$emit('edit-waypoint', waypoint)
    },

    /**
     * 删除航点
     */
    deleteWaypoint(waypointId) {
      if (confirm('确定要删除这个航点吗？')) {
        missionStore.actions.removeWaypoint(waypointId)
      }
    },

    /**
     * 清除所有航点
     */
    clearAllWaypoints() {
      if (confirm('确定要清除所有航点吗？此操作不可撤销。')) {
        const waypointIds = [...this.waypoints.map(wp => wp.id)]
        waypointIds.forEach(id => {
          missionStore.actions.removeWaypoint(id)
        })
      }
    },

    /**
     * 开始拖拽排序
     */
    startDrag(waypoint, index) {
      // 这里可以实现拖拽排序功能
      console.log('开始拖拽航点:', waypoint, index)
    }
  }
}
</script>

<style scoped>
.waypoint-manager {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.manager-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: bold;
}

.header-title i {
  color: #00aaff;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover:not([disabled]) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.action-btn.active {
  background: rgba(0, 170, 255, 0.3);
  border-color: rgba(0, 170, 255, 0.5);
  color: #00aaff;
}

.action-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.waypoint-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
}

.waypoint-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.waypoint-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.waypoint-item.selected {
  background: rgba(0, 170, 255, 0.15);
  border-color: rgba(0, 170, 255, 0.4);
}

.waypoint-item.collision {
  border-color: rgba(255, 165, 0, 0.6);
  background: rgba(255, 165, 0, 0.1);
}

.waypoint-number {
  width: 24px;
  height: 24px;
  background: #00aaff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.waypoint-item.collision .waypoint-number {
  background: #ff6600;
}

.waypoint-info {
  flex: 1;
  min-width: 0;
}

.waypoint-coords {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.coord-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  min-width: 30px;
}

.coord-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

.waypoint-params {
  display: flex;
  gap: 12px;
  margin-top: 4px;
}

.param-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 10px;
}

.param-item i {
  color: #00aaff;
  font-size: 10px;
}

.waypoint-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.waypoint-action-btn {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.waypoint-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.delete-btn:hover {
  background: rgba(255, 69, 58, 0.3);
  border-color: rgba(255, 69, 58, 0.5);
  color: #ff453a;
}

.collision-warning {
  position: absolute;
  top: -8px;
  right: 8px;
  background: #ff6600;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 12px;
  opacity: 0.8;
}

.waypoint-stats {
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.stat-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: bold;
}

.stat-label.collision,
.stat-value.collision {
  color: #ff6600;
}

/* 滚动条样式 */
.waypoint-list::-webkit-scrollbar {
  width: 6px;
}

.waypoint-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.waypoint-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.waypoint-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
