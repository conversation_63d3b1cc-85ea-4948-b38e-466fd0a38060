/**
 * 飞行任务数据模型
 * 定义飞行任务的基本属性和方法
 */

import { Waypoint } from './Waypoint.js'

export class MissionTask {
  constructor(options = {}) {
    this.id = options.id || this.generateId()
    this.name = options.name || '新建任务'
    this.type = options.type || MissionTypes.MANUAL // 任务类型
    this.description = options.description || ''
    this.waypoints = options.waypoints ? options.waypoints.map(wp => 
      wp instanceof Waypoint ? wp : Waypoint.fromJSON(wp)
    ) : []
    this.parameters = options.parameters || {} // 任务特定参数
    this.status = options.status || MissionStatus.DRAFT
    this.createdAt = options.createdAt || new Date()
    this.updatedAt = options.updatedAt || new Date()
    this.metadata = options.metadata || {}
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return 'mission_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 添加航点
   */
  addWaypoint(waypoint) {
    if (!(waypoint instanceof Waypoint)) {
      waypoint = new Waypoint(waypoint)
    }
    waypoint.order = this.waypoints.length
    this.waypoints.push(waypoint)
    this.updatedAt = new Date()
    return waypoint
  }

  /**
   * 删除航点
   */
  removeWaypoint(waypointId) {
    const index = this.waypoints.findIndex(wp => wp.id === waypointId)
    if (index !== -1) {
      this.waypoints.splice(index, 1)
      this.reorderWaypoints()
      this.updatedAt = new Date()
      return true
    }
    return false
  }

  /**
   * 更新航点
   */
  updateWaypoint(waypointId, properties) {
    const waypoint = this.waypoints.find(wp => wp.id === waypointId)
    if (waypoint) {
      waypoint.update(properties)
      this.updatedAt = new Date()
      return waypoint
    }
    return null
  }

  /**
   * 重新排序航点
   */
  reorderWaypoints() {
    this.waypoints.forEach((waypoint, index) => {
      waypoint.order = index
    })
  }

  /**
   * 移动航点顺序
   */
  moveWaypoint(fromIndex, toIndex) {
    if (fromIndex >= 0 && fromIndex < this.waypoints.length &&
        toIndex >= 0 && toIndex < this.waypoints.length) {
      const waypoint = this.waypoints.splice(fromIndex, 1)[0]
      this.waypoints.splice(toIndex, 0, waypoint)
      this.reorderWaypoints()
      this.updatedAt = new Date()
      return true
    }
    return false
  }

  /**
   * 获取任务统计信息
   */
  getStatistics() {
    const stats = {
      waypointCount: this.waypoints.length,
      totalDistance: 0,
      estimatedTime: 0,
      averageSpeed: 0,
      maxAltitude: 0,
      minAltitude: Infinity,
      collisionCount: 0
    }

    if (this.waypoints.length === 0) {
      stats.minAltitude = 0
      return stats
    }

    // 计算总距离和其他统计信息
    for (let i = 0; i < this.waypoints.length; i++) {
      const waypoint = this.waypoints[i]
      
      // 高度统计
      stats.maxAltitude = Math.max(stats.maxAltitude, waypoint.altitude)
      stats.minAltitude = Math.min(stats.minAltitude, waypoint.altitude)
      
      // 碰撞统计
      if (waypoint.isCollision) {
        stats.collisionCount++
      }
      
      // 距离计算（需要下一个航点）
      if (i < this.waypoints.length - 1) {
        const nextWaypoint = this.waypoints[i + 1]
        const distance = this.calculateDistance(waypoint, nextWaypoint)
        stats.totalDistance += distance
        
        // 时间估算（距离/速度 + 悬停时间）
        stats.estimatedTime += (distance / waypoint.speed) + waypoint.hoverTime
      } else {
        // 最后一个航点只计算悬停时间
        stats.estimatedTime += waypoint.hoverTime
      }
    }

    // 平均速度
    if (this.waypoints.length > 0) {
      const totalSpeed = this.waypoints.reduce((sum, wp) => sum + wp.speed, 0)
      stats.averageSpeed = totalSpeed / this.waypoints.length
    }

    return stats
  }

  /**
   * 计算两个航点之间的距离（使用Haversine公式）
   */
  calculateDistance(waypoint1, waypoint2) {
    const R = 6371000 // 地球半径（米）
    const lat1Rad = waypoint1.latitude * Math.PI / 180
    const lat2Rad = waypoint2.latitude * Math.PI / 180
    const deltaLatRad = (waypoint2.latitude - waypoint1.latitude) * Math.PI / 180
    const deltaLngRad = (waypoint2.longitude - waypoint1.longitude) * Math.PI / 180

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

    return R * c
  }

  /**
   * 验证任务数据
   */
  validate() {
    const errors = []
    
    if (!this.name || this.name.trim().length === 0) {
      errors.push('任务名称不能为空')
    }
    
    if (!Object.values(MissionTypes).includes(this.type)) {
      errors.push('无效的任务类型')
    }
    
    if (this.waypoints.length === 0) {
      errors.push('任务至少需要一个航点')
    }
    
    // 验证每个航点
    this.waypoints.forEach((waypoint, index) => {
      const validation = waypoint.validate()
      if (!validation.isValid) {
        errors.push(`航点${index + 1}: ${validation.errors.join(', ')}`)
      }
    })
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 转换为JSON格式
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      description: this.description,
      waypoints: this.waypoints.map(wp => wp.toJSON()),
      parameters: this.parameters,
      status: this.status,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      metadata: this.metadata
    }
  }

  /**
   * 从JSON创建任务实例
   */
  static fromJSON(json) {
    return new MissionTask(json)
  }

  /**
   * 克隆任务
   */
  clone() {
    const cloned = new MissionTask(this.toJSON())
    cloned.id = this.generateId() // 生成新的ID
    cloned.name = this.name + ' (副本)'
    return cloned
  }
}

/**
 * 任务类型枚举
 */
export const MissionTypes = {
  MANUAL: 'manual',         // 手动航点任务
  SURVEY: 'survey',         // 区域测绘
  CORRIDOR: 'corridor',     // 廊道扫描
  STRUCTURE: 'structure'    // 结构扫描
}

/**
 * 任务状态枚举
 */
export const MissionStatus = {
  DRAFT: 'draft',           // 草稿
  READY: 'ready',           // 就绪
  RUNNING: 'running',       // 执行中
  PAUSED: 'paused',         // 暂停
  COMPLETED: 'completed',   // 已完成
  FAILED: 'failed',         // 失败
  CANCELLED: 'cancelled'    // 已取消
}

/**
 * 任务类型描述
 */
export const MissionTypeLabels = {
  [MissionTypes.MANUAL]: '手动航点',
  [MissionTypes.SURVEY]: '区域测绘',
  [MissionTypes.CORRIDOR]: '廊道扫描',
  [MissionTypes.STRUCTURE]: '结构扫描'
}

/**
 * 任务状态描述
 */
export const MissionStatusLabels = {
  [MissionStatus.DRAFT]: '草稿',
  [MissionStatus.READY]: '就绪',
  [MissionStatus.RUNNING]: '执行中',
  [MissionStatus.PAUSED]: '暂停',
  [MissionStatus.COMPLETED]: '已完成',
  [MissionStatus.FAILED]: '失败',
  [MissionStatus.CANCELLED]: '已取消'
}
