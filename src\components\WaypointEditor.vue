<template>
  <div v-if="visible" class="waypoint-editor-overlay" @click="closeEditor">
    <div class="waypoint-editor" @click.stop>
      <div class="editor-header">
        <div class="header-title">
          <i class="fas fa-edit"></i>
          <span>编辑航点 {{ waypointIndex + 1 }}</span>
        </div>
        <button class="close-btn" @click="closeEditor">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="editor-content">
        <!-- 位置信息 -->
        <div class="form-section">
          <h4 class="section-title">位置信息</h4>
          <div class="form-row">
            <div class="form-group">
              <label>纬度</label>
              <input 
                type="number" 
                v-model.number="editData.latitude"
                step="0.000001"
                class="form-input"
                @input="validatePosition"
              >
            </div>
            <div class="form-group">
              <label>经度</label>
              <input 
                type="number" 
                v-model.number="editData.longitude"
                step="0.000001"
                class="form-input"
                @input="validatePosition"
              >
            </div>
          </div>
        </div>

        <!-- 飞行参数 -->
        <div class="form-section">
          <h4 class="section-title">飞行参数</h4>
          <div class="form-row">
            <div class="form-group">
              <label>飞行高度 (米)</label>
              <input 
                type="number" 
                v-model.number="editData.altitude"
                min="10"
                max="1000"
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>飞行速度 (m/s)</label>
              <input 
                type="number" 
                v-model.number="editData.speed"
                min="1"
                max="30"
                step="0.1"
                class="form-input"
              >
            </div>
          </div>
        </div>

        <!-- 动作设置 -->
        <div class="form-section">
          <h4 class="section-title">动作设置</h4>
          <div class="form-row">
            <div class="form-group">
              <label>执行动作</label>
              <select v-model="editData.action" class="form-select">
                <option value="hover">悬停</option>
                <option value="photo">拍照</option>
                <option value="video_start">开始录像</option>
                <option value="video_stop">停止录像</option>
                <option value="wait">等待</option>
                <option value="custom">自定义</option>
              </select>
            </div>
            <div class="form-group">
              <label>悬停时间 (秒)</label>
              <input 
                type="number" 
                v-model.number="editData.hoverTime"
                min="0"
                max="60"
                step="0.1"
                class="form-input"
              >
            </div>
          </div>
        </div>

        <!-- 地形信息 -->
        <div class="form-section" v-if="editData.terrainHeight !== null">
          <h4 class="section-title">地形信息</h4>
          <div class="terrain-info">
            <div class="terrain-item">
              <span class="terrain-label">地面高度:</span>
              <span class="terrain-value">{{ editData.terrainHeight?.toFixed(1) || 'N/A' }}m</span>
            </div>
            <div class="terrain-item">
              <span class="terrain-label">相对高度:</span>
              <span class="terrain-value">{{ relativeAltitude.toFixed(1) }}m</span>
            </div>
            <div class="terrain-item" v-if="editData.isCollision">
              <span class="terrain-label collision">碰撞风险:</span>
              <span class="terrain-value collision">是</span>
            </div>
          </div>
        </div>

        <!-- 验证错误 -->
        <div v-if="validationErrors.length" class="validation-errors">
          <div class="error-title">
            <i class="fas fa-exclamation-triangle"></i>
            <span>输入错误</span>
          </div>
          <ul class="error-list">
            <li v-for="error in validationErrors" :key="error">{{ error }}</li>
          </ul>
        </div>
      </div>

      <div class="editor-footer">
        <button class="btn btn-secondary" @click="closeEditor">取消</button>
        <button class="btn btn-primary" @click="saveChanges" :disabled="!isValid">保存</button>
      </div>
    </div>
  </div>
</template>

<script>
import { missionStore } from '@/stores/missionStore.js'
import { WaypointActions, WaypointActionLabels } from '@/models/Waypoint.js'

export default {
  name: 'WaypointEditor',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    waypoint: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'save'],
  data() {
    return {
      editData: {
        latitude: 0,
        longitude: 0,
        altitude: 100,
        speed: 10,
        action: 'hover',
        hoverTime: 3,
        terrainHeight: null,
        isCollision: false
      },
      validationErrors: []
    }
  },
  computed: {
    waypointIndex() {
      if (!this.waypoint || !missionStore.state.currentMission) return 0
      return missionStore.state.currentMission.waypoints.findIndex(wp => wp.id === this.waypoint.id)
    },
    relativeAltitude() {
      if (this.editData.terrainHeight === null) return this.editData.altitude
      return this.editData.altitude - this.editData.terrainHeight
    },
    isValid() {
      return this.validationErrors.length === 0
    }
  },
  watch: {
    waypoint: {
      handler(newWaypoint) {
        if (newWaypoint) {
          this.loadWaypointData(newWaypoint)
        }
      },
      immediate: true
    },
    visible(newVisible) {
      if (newVisible && this.waypoint) {
        this.loadWaypointData(this.waypoint)
      }
    }
  },
  methods: {
    /**
     * 加载航点数据
     */
    loadWaypointData(waypoint) {
      this.editData = {
        latitude: waypoint.latitude,
        longitude: waypoint.longitude,
        altitude: waypoint.altitude,
        speed: waypoint.speed,
        action: waypoint.action,
        hoverTime: waypoint.hoverTime,
        terrainHeight: waypoint.terrainHeight,
        isCollision: waypoint.isCollision
      }
      this.validateAll()
    },

    /**
     * 验证位置
     */
    validatePosition() {
      this.validateAll()
    },

    /**
     * 验证所有输入
     */
    validateAll() {
      this.validationErrors = []

      // 验证纬度
      if (this.editData.latitude < -90 || this.editData.latitude > 90) {
        this.validationErrors.push('纬度必须在-90到90之间')
      }

      // 验证经度
      if (this.editData.longitude < -180 || this.editData.longitude > 180) {
        this.validationErrors.push('经度必须在-180到180之间')
      }

      // 验证高度
      if (this.editData.altitude < 10 || this.editData.altitude > 1000) {
        this.validationErrors.push('飞行高度必须在10到1000米之间')
      }

      // 验证速度
      if (this.editData.speed < 1 || this.editData.speed > 30) {
        this.validationErrors.push('飞行速度必须在1到30m/s之间')
      }

      // 验证悬停时间
      if (this.editData.hoverTime < 0 || this.editData.hoverTime > 60) {
        this.validationErrors.push('悬停时间必须在0到60秒之间')
      }
    },

    /**
     * 保存更改
     */
    saveChanges() {
      if (!this.isValid || !this.waypoint) return

      const updates = {
        position: [this.editData.latitude, this.editData.longitude],
        altitude: this.editData.altitude,
        speed: this.editData.speed,
        action: this.editData.action,
        hoverTime: this.editData.hoverTime
      }

      missionStore.actions.updateWaypoint(this.waypoint.id, updates)
      this.$emit('save', updates)
      this.closeEditor()
    },

    /**
     * 关闭编辑器
     */
    closeEditor() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.waypoint-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
}

.waypoint-editor {
  width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  background: rgba(15, 25, 40, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: bold;
}

.header-title i {
  color: #00aaff;
  font-size: 18px;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 69, 58, 0.3);
  border-color: rgba(255, 69, 58, 0.5);
  color: #ff453a;
}

.editor-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin-bottom: 6px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: rgba(0, 170, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.2);
}

.terrain-info {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 12px;
}

.terrain-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.terrain-item:last-child {
  margin-bottom: 0;
}

.terrain-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.terrain-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: bold;
}

.terrain-label.collision,
.terrain-value.collision {
  color: #ff6600;
}

.validation-errors {
  background: rgba(255, 69, 58, 0.1);
  border: 1px solid rgba(255, 69, 58, 0.3);
  border-radius: 6px;
  padding: 12px;
  margin-top: 16px;
}

.error-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff453a;
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 8px;
}

.error-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.error-list li {
  color: #ff453a;
  font-size: 11px;
  margin-bottom: 4px;
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-primary {
  background: #00aaff;
  border-color: #00aaff;
  color: white;
}

.btn-primary:hover:not([disabled]) {
  background: #0088cc;
  border-color: #0088cc;
}

.btn-primary[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 滚动条样式 */
.editor-content::-webkit-scrollbar {
  width: 6px;
}

.editor-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.editor-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.editor-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
