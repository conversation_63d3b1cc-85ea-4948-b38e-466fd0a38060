/**
 * 航点数据模型
 * 定义航点的基本属性和方法
 */

export class Waypoint {
  constructor(options = {}) {
    this.id = options.id || this.generateId()
    this.position = options.position || [39.9042, 116.4074] // [纬度, 经度]
    this.altitude = options.altitude || 100 // 飞行高度（米）
    this.speed = options.speed || 10 // 飞行速度（m/s）
    this.action = options.action || 'hover' // 执行动作
    this.hoverTime = options.hoverTime || 3 // 悬停时间（秒）
    this.order = options.order || 0 // 航点顺序
    this.terrainHeight = options.terrainHeight || null // 地形高度
    this.isCollision = options.isCollision || false // 是否存在碰撞风险
    this.createdAt = options.createdAt || new Date()
    this.updatedAt = options.updatedAt || new Date()
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return 'wp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 获取纬度
   */
  get latitude() {
    return this.position[0]
  }

  /**
   * 获取经度
   */
  get longitude() {
    return this.position[1]
  }

  /**
   * 设置位置
   */
  setPosition(lat, lng) {
    this.position = [lat, lng]
    this.updatedAt = new Date()
  }

  /**
   * 更新航点属性
   */
  update(properties) {
    Object.keys(properties).forEach(key => {
      if (this.hasOwnProperty(key)) {
        this[key] = properties[key]
      }
    })
    this.updatedAt = new Date()
  }

  /**
   * 验证航点数据
   */
  validate() {
    const errors = []
    
    if (!Array.isArray(this.position) || this.position.length !== 2) {
      errors.push('位置坐标格式错误')
    }
    
    if (this.altitude < 0 || this.altitude > 1000) {
      errors.push('飞行高度应在0-1000米之间')
    }
    
    if (this.speed < 1 || this.speed > 30) {
      errors.push('飞行速度应在1-30m/s之间')
    }
    
    if (this.hoverTime < 0 || this.hoverTime > 60) {
      errors.push('悬停时间应在0-60秒之间')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 转换为JSON格式
   */
  toJSON() {
    return {
      id: this.id,
      position: this.position,
      altitude: this.altitude,
      speed: this.speed,
      action: this.action,
      hoverTime: this.hoverTime,
      order: this.order,
      terrainHeight: this.terrainHeight,
      isCollision: this.isCollision,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    }
  }

  /**
   * 从JSON创建航点实例
   */
  static fromJSON(json) {
    return new Waypoint(json)
  }

  /**
   * 克隆航点
   */
  clone() {
    return new Waypoint(this.toJSON())
  }
}

/**
 * 航点动作类型枚举
 */
export const WaypointActions = {
  HOVER: 'hover',           // 悬停
  PHOTO: 'photo',           // 拍照
  VIDEO_START: 'video_start', // 开始录像
  VIDEO_STOP: 'video_stop',   // 停止录像
  GIMBAL_PITCH: 'gimbal_pitch', // 云台俯仰
  GIMBAL_YAW: 'gimbal_yaw',     // 云台偏航
  WAIT: 'wait',             // 等待
  CUSTOM: 'custom'          // 自定义动作
}

/**
 * 航点动作描述
 */
export const WaypointActionLabels = {
  [WaypointActions.HOVER]: '悬停',
  [WaypointActions.PHOTO]: '拍照',
  [WaypointActions.VIDEO_START]: '开始录像',
  [WaypointActions.VIDEO_STOP]: '停止录像',
  [WaypointActions.GIMBAL_PITCH]: '云台俯仰',
  [WaypointActions.GIMBAL_YAW]: '云台偏航',
  [WaypointActions.WAIT]: '等待',
  [WaypointActions.CUSTOM]: '自定义'
}
