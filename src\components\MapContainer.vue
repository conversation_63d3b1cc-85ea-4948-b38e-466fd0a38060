<template>
  <div class="map-container">
    <!-- 真实地图显示 -->
    <div id="realMap" ref="mapContainer"></div>

    <div class="map-overlay"></div>

    <!-- 图层选择弹窗 -->
    <div v-if="showLayerPanel" class="layer-panel">
      <div class="layer-panel-header">
        <i class="fas fa-layer-group"></i>
        <h3>图层选择</h3>
        <button class="close-btn" @click="showLayerPanel = false">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="layer-panel-content">
        <!-- 基础地图选择 -->
        <div class="layer-section">
          <h4>基础地图</h4>
          <div class="layer-options">
            <div
              v-for="mapType in availableMapTypes"
              :key="mapType.id"
              class="layer-option"
              :class="{ active: currentMapLayer === mapType.id }"
              @click="switchBaseMap(mapType.id)"
            >
              <i :class="mapType.icon"></i>
              <div class="layer-info">
                <div class="layer-name">{{ mapType.name }}</div>
                <div class="layer-desc">{{ mapType.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 可选图层 -->
        <div class="layer-section">
          <h4>可选图层</h4>
          <div class="overlay-options">
            <div
              v-for="overlay in relevantOverlays"
              :key="overlay.id"
              class="overlay-option"
              @click="toggleOverlay(overlay.id)"
            >
              <div class="overlay-toggle">
                <input
                  type="checkbox"
                  :checked="overlayStates[overlay.id]"
                  @change="toggleOverlay(overlay.id)"
                >
                <i :class="overlay.icon"></i>
                <div class="overlay-info">
                  <div class="overlay-name">{{ overlay.name }}</div>
                  <div class="overlay-desc">{{ overlay.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 飞行状态HUD -->
    <div class="flight-hud">
      <div class="hud-left">
        <div class="drone-info">
          <div class="drone-id">UAV-001</div>
          <div class="flight-mode">AUTO</div>
        </div>
        <div class="connection-status">
          <div class="signal-indicator">
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar active"></div>
          </div>
          <span class="signal-text">LINK</span>
        </div>
      </div>

      <div class="hud-center">
        <div class="mission-time">{{ currentTime }}</div>
        <div class="coordinates">39°54'15"N 116°24'27"E</div>
      </div>

      <div class="hud-right">
        <div class="telemetry-item">
          <span class="telem-label">ALT</span>
          <span class="telem-value">120m</span>
        </div>
        <div class="telemetry-item">
          <span class="telem-label">SPD</span>
          <span class="telem-value">15m/s</span>
        </div>
        <div class="telemetry-item">
          <span class="telem-label">BAT</span>
          <span class="telem-value">87%</span>
        </div>
        <div class="telemetry-item">
          <span class="telem-label">RTK</span>
          <span class="telem-value rtk-fix">FIX</span>
        </div>
      </div>
    </div>

    <!-- 无人机标记 (HTML覆盖层) -->
    <div
      class="drone-marker"
      :style="{ top: dronePosition.top, left: dronePosition.left }"
    >
      <div class="drone-radar-ring"></div>
      <div class="drone-radar-pulse"></div>
      <div class="drone-icon-container">
        <div class="drone-body">
          <div class="drone-center">
            <div class="drone-led"></div>
          </div>
          <div class="drone-arm drone-arm-1">
            <div class="drone-propeller"></div>
          </div>
          <div class="drone-arm drone-arm-2">
            <div class="drone-propeller"></div>
          </div>
          <div class="drone-arm drone-arm-3">
            <div class="drone-propeller"></div>
          </div>
          <div class="drone-arm drone-arm-4">
            <div class="drone-propeller"></div>
          </div>
        </div>
        <div class="drone-label">UAV-001</div>
        <div class="drone-status">在线</div>
      </div>
    </div>



    <!-- 无人机操控按钮组 -->
    <div class="drone-controls">
      <div
        v-for="control in droneControls"
        :key="control.name"
        class="drone-control-btn"
        :class="{
          active: control.active,
          emergency: control.emergency
        }"
        :title="control.name"
        @click="handleDroneControl(control)"
      >
        <i :class="control.icon"></i>
        <span class="btn-label">{{ control.name }}</span>
      </div>
    </div>

    <!-- 地图控制按钮 -->
    <div class="map-controls">
      <div class="map-control-btn" title="放大" @click="zoomIn">
        <i class="fas fa-plus"></i>
      </div>
      <div class="map-control-btn" title="缩小" @click="zoomOut">
        <i class="fas fa-minus"></i>
      </div>
      <div class="map-control-btn" title="定位" @click="centerMap">
        <i class="fas fa-crosshairs"></i>
      </div>
      <div class="map-control-btn" title="图层" @click="toggleLayer">
        <i class="fas fa-layer-group"></i>
      </div>
    </div>

    <!-- 航点编辑控制按钮 -->
    <div class="waypoint-controls">
      <div
        class="waypoint-control-btn"
        :class="{ active: missionStore.state.mapMode === 'add_waypoint' }"
        title="添加航点"
        @click="toggleMapMode('add_waypoint')"
      >
        <i class="fas fa-map-pin"></i>
      </div>
      <div
        class="waypoint-control-btn"
        title="适应航点"
        @click="fitWaypointsBounds"
        :disabled="!missionStore.state.currentMission?.waypoints.length"
      >
        <i class="fas fa-expand-arrows-alt"></i>
      </div>
      <div
        class="waypoint-control-btn"
        title="清除所有航点"
        @click="clearAllWaypoints"
        :disabled="!missionStore.state.currentMission?.waypoints.length"
      >
        <i class="fas fa-trash-alt"></i>
      </div>
    </div>



    <!-- 状态提示 -->
    <div v-if="statusMessage" class="status-toast">
      {{ statusMessage }}
    </div>
  </div>
</template>

<script>
import { MAP_CONFIG, createMapTypeGroup, getAvailableMapTypes, getAvailableOverlays, createOverlayLayer } from '@/utils/mapConfig'
import { missionStore } from '@/stores/missionStore.js'
import { Waypoint } from '@/models/Waypoint.js'
import L from 'leaflet'

export default {
  name: 'MapContainer',
  components: {
  },
  setup() {
    return {
      missionStore
    }
  },
  computed: {
    // 根据当前地图类型智能显示相关图层
    relevantOverlays() {
      const allOverlays = this.availableOverlays

      // 根据当前地图类型过滤相关图层
      if (this.currentMapLayer === 'vector') {
        // 矢量地图显示路网图层
        return allOverlays.filter(overlay =>
          overlay.id === 'roadNetwork' || overlay.id === 'traffic'
        )
      } else if (this.currentMapLayer === 'satellite') {
        // 卫星地图显示卫星标注和交通图层
        return allOverlays.filter(overlay =>
          overlay.id === 'satelliteLabels' || overlay.id === 'traffic'
        )
      } else if (this.currentMapLayer === 'terrain') {
        // 地形地图显示地形标注和交通图层
        return allOverlays.filter(overlay =>
          overlay.id === 'terrainLabels' || overlay.id === 'traffic'
        )
      }

      // 默认显示所有图层
      return allOverlays
    }
  },
  data() {
    return {
      map: null,
      droneMarker: null,

      dronePosition: {
        top: '45%',
        left: '45%'
      },
      currentTime: '',
      currentMapType: '卫星地图',
      bandwidth: 58,
      currentMapLayer: 'satellite',
      availableMapTypes: getAvailableMapTypes(),
      availableOverlays: getAvailableOverlays(),
      mapLayers: {},
      overlayLayers: {},
      overlayStates: {
        roadNetwork: false,
        satelliteLabels: false,
        terrainLabels: false,
        traffic: false
      },
      showLayerPanel: false,
      statusMessage: '',
      droneControls: [
        { name: '起飞', icon: 'fas fa-rocket', active: false, emergency: false },
        { name: '降落', icon: 'fas fa-landmark', active: false, emergency: false },
        { name: '悬停', icon: 'fas fa-pause-circle', active: false, emergency: false },
        { name: '返航', icon: 'fas fa-undo-alt', active: false, emergency: false },
        { name: '停止', icon: 'fas fa-hand-paper', active: false, emergency: true },
        { name: '锁定', icon: 'fas fa-lock', active: false, emergency: false }
      ],
      currentMode: null,

      // 航点相关数据
      waypointMarkers: new Map(), // 航点标记映射
      pathPolylines: [],          // 路径线条
      isAddingWaypoint: false,    // 是否处于添加航点模式
      draggedWaypoint: null,      // 当前拖拽的航点

      // 自定义图标
      waypointIcon: null,
      selectedWaypointIcon: null
    }
  },
  mounted() {
    this.updateTime()
    setInterval(this.updateTime, 1000)

    // 延迟初始化地图，确保DOM已经渲染
    this.$nextTick(() => {
      setTimeout(() => {
        this.initMap()
        this.initWaypointIcons()
        this.setupMapEvents()
      }, 500) // 增加延迟时间
    })
  },
  methods: {
    initMap() {
      try {
        // 检查DOM元素是否存在
        const mapElement = document.getElementById('realMap')
        if (!mapElement) {
          console.error('地图容器元素未找到')
          return
        }

        // 初始化地图
        this.map = L.map('realMap', {
          center: [39.9042, 116.4074], // 北京天安门
          zoom: 13,
          zoomControl: false
        })

        // 创建所有地图图层
        this.availableMapTypes.forEach(mapType => {
          this.mapLayers[mapType.id] = createMapTypeGroup(mapType.id, MAP_CONFIG.TIANDITU_API_KEY)
        })

        // 预创建可选图层（但不添加到地图）
        this.availableOverlays.forEach(overlay => {
          this.overlayLayers[overlay.id] = createOverlayLayer(overlay.id, MAP_CONFIG.TIANDITU_API_KEY)
        })

        // 添加默认图层
        this.map.addLayer(this.mapLayers[this.currentMapLayer])
      } catch (error) {
        console.error('地图初始化失败:', error)
      }
    },

    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },



    handleDroneControl(control) {
      // 重置所有控制状态
      this.droneControls.forEach(ctrl => {
        ctrl.active = false
      })

      // 激活当前控制
      control.active = true
      this.currentMode = control.name

      // 显示状态消息
      this.statusMessage = `执行${control.name}操作...`
      setTimeout(() => {
        this.statusMessage = ''
        control.active = false
        this.currentMode = null
      }, 2000)
    },

    zoomIn() {
      this.map.zoomIn()
    },

    zoomOut() {
      this.map.zoomOut()
    },

    centerMap() {
      this.map.setView([39.9042, 116.4074], 13)
    },

    toggleLayer() {
      // 显示图层选择弹窗
      this.showLayerPanel = !this.showLayerPanel
    },

    switchBaseMap(mapTypeId) {
      if (mapTypeId === this.currentMapLayer) return

      // 移除当前基础地图图层
      if (this.mapLayers[this.currentMapLayer]) {
        this.map.removeLayer(this.mapLayers[this.currentMapLayer])
      }

      // 智能关闭不相关的图层
      this.autoManageOverlays(this.currentMapLayer)

      // 添加新的基础地图图层
      if (this.mapLayers[mapTypeId]) {
        this.map.addLayer(this.mapLayers[mapTypeId])
      }

      // 更新状态
      this.currentMapLayer = mapTypeId
      const mapType = this.availableMapTypes.find(type => type.id === mapTypeId)
      this.currentMapType = mapType ? mapType.name : '未知地图'

      // 显示切换提示
      this.statusMessage = `已切换到${this.currentMapType}`
      setTimeout(() => {
        this.statusMessage = ''
      }, 2000)

      // 自动关闭图层选择面板
      this.showLayerPanel = false
    },

    // 智能管理图层，切换地图类型时自动处理相关图层
    autoManageOverlays(fromMapType) {
      // 定义每种地图类型的专用图层
      const mapSpecificLayers = {
        vector: ['roadNetwork'],
        satellite: ['satelliteLabels'],
        terrain: ['terrainLabels']
      }

      // 关闭旧地图类型的专用图层
      if (mapSpecificLayers[fromMapType]) {
        mapSpecificLayers[fromMapType].forEach(layerId => {
          if (this.overlayStates[layerId]) {
            this.toggleOverlay(layerId)
          }
        })
      }


    },

    toggleOverlay(overlayId) {
      const isEnabled = this.overlayStates[overlayId]

      if (isEnabled) {
        // 移除图层
        if (this.overlayLayers[overlayId]) {
          this.map.removeLayer(this.overlayLayers[overlayId])
        }
        this.overlayStates[overlayId] = false
      } else {
        // 添加图层
        if (overlayId === 'traffic') {
          // 交通图层特殊处理 - 演示功能
          this.handleTrafficLayer()
        } else {
          // 其他图层正常处理
          if (!this.overlayLayers[overlayId]) {
            this.overlayLayers[overlayId] = createOverlayLayer(overlayId, MAP_CONFIG.TIANDITU_API_KEY)
          }
          this.map.addLayer(this.overlayLayers[overlayId])
        }
        this.overlayStates[overlayId] = true
      }

      // 显示状态提示
      const overlay = this.availableOverlays.find(o => o.id === overlayId)
      const action = this.overlayStates[overlayId] ? '已开启' : '已关闭'
      this.statusMessage = `${overlay.name}${action}`
      setTimeout(() => {
        this.statusMessage = ''
      }, 2000)

      // 自动关闭图层选择面板
      this.showLayerPanel = false
    },

    // 处理交通图层 - 演示功能
    handleTrafficLayer() {
      // 这里可以集成真实的交通API，比如：
      // - 百度地图交通图层
      // - 高德地图交通图层
      // - Google Maps交通图层
      //
      // 当前作为演示，显示提示信息
      this.statusMessage = '交通图层功能演示 - 可集成实时交通数据API'

      // 可以在这里添加一些演示的交通标记
      // 比如在主要道路上添加交通状况指示器
      console.log('交通图层已开启 - 演示模式')
    },

    // ==================== 航点编辑功能 ====================

    /**
     * 初始化航点图标
     */
    initWaypointIcons() {
      // 普通航点图标
      this.waypointIcon = L.divIcon({
        className: 'waypoint-marker',
        html: `
          <div class="waypoint-icon">
            <div class="waypoint-number">1</div>
          </div>
        `,
        iconSize: [30, 30],
        iconAnchor: [15, 15]
      })

      // 选中航点图标
      this.selectedWaypointIcon = L.divIcon({
        className: 'waypoint-marker selected',
        html: `
          <div class="waypoint-icon selected">
            <div class="waypoint-number">1</div>
          </div>
        `,
        iconSize: [30, 30],
        iconAnchor: [15, 15]
      })
    },

    /**
     * 设置地图事件监听
     */
    setupMapEvents() {
      if (!this.map) return

      // 地图点击事件 - 添加航点
      this.map.on('click', (e) => {
        if (missionStore.state.mapMode === 'add_waypoint') {
          this.addWaypointAtPosition(e.latlng)
        }
      })

      // 监听任务状态变化
      this.$watch(() => missionStore.state.currentMission, (newMission, oldMission) => {
        this.updateWaypointsDisplay()
      }, { deep: true })

      // 监听航点变化
      this.$watch(() => missionStore.state.currentMission?.waypoints, (newWaypoints) => {
        this.updateWaypointsDisplay()
      }, { deep: true })

      // 监听选中航点变化
      this.$watch(() => missionStore.state.selectedWaypoint, (newSelected, oldSelected) => {
        this.updateWaypointSelection(newSelected, oldSelected)
      })
    },

    /**
     * 在指定位置添加航点
     */
    addWaypointAtPosition(latlng) {
      const waypoint = missionStore.actions.addWaypoint([latlng.lat, latlng.lng])
      if (waypoint) {
        this.statusMessage = `已添加航点 ${waypoint.order + 1}`
        setTimeout(() => {
          this.statusMessage = ''
        }, 2000)
      }
    },

    /**
     * 更新航点显示
     */
    updateWaypointsDisplay() {
      // 清除现有标记和路径
      this.clearWaypointsDisplay()

      const mission = missionStore.state.currentMission
      if (!mission || !mission.waypoints.length) return

      // 添加航点标记
      mission.waypoints.forEach((waypoint, index) => {
        this.addWaypointMarker(waypoint, index)
      })

      // 绘制路径
      this.drawFlightPath(mission.waypoints)
    },

    /**
     * 清除航点显示
     */
    clearWaypointsDisplay() {
      // 清除航点标记
      this.waypointMarkers.forEach(marker => {
        this.map.removeLayer(marker)
      })
      this.waypointMarkers.clear()

      // 清除路径线条
      this.pathPolylines.forEach(polyline => {
        this.map.removeLayer(polyline)
      })
      this.pathPolylines = []
    },

    /**
     * 添加航点标记
     */
    addWaypointMarker(waypoint, index) {
      const icon = L.divIcon({
        className: 'waypoint-marker',
        html: `
          <div class="waypoint-icon">
            <div class="waypoint-number">${index + 1}</div>
          </div>
        `,
        iconSize: [30, 30],
        iconAnchor: [15, 15]
      })

      const marker = L.marker([waypoint.latitude, waypoint.longitude], {
        icon: icon,
        draggable: true
      })

      // 添加点击事件
      marker.on('click', () => {
        missionStore.actions.selectWaypoint(waypoint)
      })

      // 添加拖拽事件
      marker.on('dragstart', () => {
        this.draggedWaypoint = waypoint
      })

      marker.on('dragend', (e) => {
        const newPos = e.target.getLatLng()
        missionStore.actions.updateWaypoint(waypoint.id, {
          position: [newPos.lat, newPos.lng]
        })
        this.draggedWaypoint = null
      })

      // 添加到地图和缓存
      marker.addTo(this.map)
      this.waypointMarkers.set(waypoint.id, marker)
    },

    /**
     * 绘制飞行路径
     */
    drawFlightPath(waypoints) {
      if (waypoints.length < 2) return

      const coordinates = waypoints.map(wp => [wp.latitude, wp.longitude])

      const polyline = L.polyline(coordinates, {
        color: '#00aaff',
        weight: 3,
        opacity: 0.8,
        dashArray: '5, 10'
      })

      polyline.addTo(this.map)
      this.pathPolylines.push(polyline)

      // 添加方向箭头
      this.addDirectionArrows(coordinates)
    },

    /**
     * 添加方向箭头
     */
    addDirectionArrows(coordinates) {
      for (let i = 0; i < coordinates.length - 1; i++) {
        const start = coordinates[i]
        const end = coordinates[i + 1]
        const midpoint = [
          (start[0] + end[0]) / 2,
          (start[1] + end[1]) / 2
        ]

        // 计算箭头方向
        const bearing = this.calculateBearing(start, end)

        const arrowIcon = L.divIcon({
          className: 'path-arrow',
          html: `<div class="arrow-icon" style="transform: rotate(${bearing}deg)">→</div>`,
          iconSize: [20, 20],
          iconAnchor: [10, 10]
        })

        const arrowMarker = L.marker(midpoint, { icon: arrowIcon })
        arrowMarker.addTo(this.map)
        this.pathPolylines.push(arrowMarker)
      }
    },

    /**
     * 计算方位角
     */
    calculateBearing(start, end) {
      const lat1 = start[0] * Math.PI / 180
      const lat2 = end[0] * Math.PI / 180
      const deltaLng = (end[1] - start[1]) * Math.PI / 180

      const y = Math.sin(deltaLng) * Math.cos(lat2)
      const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLng)

      const bearing = Math.atan2(y, x) * 180 / Math.PI
      return (bearing + 360) % 360
    },

    /**
     * 更新航点选择状态
     */
    updateWaypointSelection(newSelected, oldSelected) {
      // 取消之前选中的航点高亮
      if (oldSelected) {
        const oldMarker = this.waypointMarkers.get(oldSelected.id)
        if (oldMarker) {
          this.updateMarkerIcon(oldMarker, oldSelected, false)
        }
      }

      // 高亮新选中的航点
      if (newSelected) {
        const newMarker = this.waypointMarkers.get(newSelected.id)
        if (newMarker) {
          this.updateMarkerIcon(newMarker, newSelected, true)
        }
      }
    },

    /**
     * 更新标记图标
     */
    updateMarkerIcon(marker, waypoint, isSelected) {
      const index = missionStore.state.currentMission.waypoints.findIndex(wp => wp.id === waypoint.id)
      const className = isSelected ? 'waypoint-marker selected' : 'waypoint-marker'

      const icon = L.divIcon({
        className: className,
        html: `
          <div class="waypoint-icon ${isSelected ? 'selected' : ''}">
            <div class="waypoint-number">${index + 1}</div>
          </div>
        `,
        iconSize: [30, 30],
        iconAnchor: [15, 15]
      })

      marker.setIcon(icon)
    },

    /**
     * 删除航点
     */
    removeWaypoint(waypointId) {
      const success = missionStore.actions.removeWaypoint(waypointId)
      if (success) {
        this.statusMessage = '航点已删除'
        setTimeout(() => {
          this.statusMessage = ''
        }, 2000)
      }
    },

    /**
     * 切换地图模式
     */
    toggleMapMode(mode) {
      if (missionStore.state.mapMode === mode) {
        missionStore.actions.setMapMode('view')
        this.statusMessage = '已退出编辑模式'
      } else {
        missionStore.actions.setMapMode(mode)
        if (mode === 'add_waypoint') {
          this.statusMessage = '点击地图添加航点'
        }
      }

      setTimeout(() => {
        this.statusMessage = ''
      }, 2000)
    },

    /**
     * 居中显示所有航点
     */
    fitWaypointsBounds() {
      const mission = missionStore.state.currentMission
      if (!mission || !mission.waypoints.length) return

      const coordinates = mission.waypoints.map(wp => [wp.latitude, wp.longitude])
      const bounds = L.latLngBounds(coordinates)
      this.map.fitBounds(bounds, { padding: [20, 20] })
    },

    /**
     * 清除所有航点
     */
    clearAllWaypoints() {
      const mission = missionStore.state.currentMission
      if (!mission || !mission.waypoints.length) return

      if (confirm('确定要清除所有航点吗？此操作不可撤销。')) {
        // 清除所有航点
        const waypointIds = [...mission.waypoints.map(wp => wp.id)]
        waypointIds.forEach(id => {
          missionStore.actions.removeWaypoint(id)
        })

        this.statusMessage = '已清除所有航点'
        setTimeout(() => {
          this.statusMessage = ''
        }, 2000)
      }
    }
  }
}
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #0f1928;
}

#realMap {
  width: 100%;
  height: 100vh;
  z-index: 1;
  position: relative;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(15, 25, 40, 0.1) 0%,
    rgba(15, 25, 40, 0.05) 50%,
    rgba(15, 25, 40, 0.1) 100%
  );
  pointer-events: none;
  z-index: 2;
}

/* 飞行状态HUD */
.flight-hud {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  max-width: 1200px;
  min-width: 800px;
  height: 50px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 25px;
  z-index: 1000;
  font-family: 'Courier New', monospace;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.hud-left {
  display: flex;
  align-items: center;
  gap: 30px;
}

.drone-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.drone-id {
  color: #ff4500;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 69, 0, 0.5);
}

.flight-mode {
  color: #00ff00;
  font-size: 10px;
  font-weight: bold;
  background: rgba(0, 255, 0, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(0, 255, 0, 0.3);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal-indicator {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 16px;
}

.signal-bar {
  width: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
}

.signal-bar:nth-child(1) { height: 4px; }
.signal-bar:nth-child(2) { height: 8px; }
.signal-bar:nth-child(3) { height: 12px; }
.signal-bar:nth-child(4) { height: 16px; }

.signal-bar.active {
  background: #00ff00;
  box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

.signal-text {
  color: #00ff00;
  font-size: 10px;
  font-weight: bold;
}

.hud-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.mission-time {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.coordinates {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  font-weight: normal;
}

.hud-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.telemetry-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.telem-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 9px;
  font-weight: bold;
}

.telem-value {
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.rtk-fix {
  color: #00ff00;
  text-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
}

/* 无人机标记 */
.drone-marker {
  position: absolute;
  z-index: 100;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* 雷达环 */
.drone-radar-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 69, 0, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: radar-ring 3s infinite;
}

.drone-radar-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border: 2px solid rgba(255, 69, 0, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: radar-pulse 2s infinite;
}

@keyframes radar-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes radar-pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
}

.drone-icon-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 无人机机身 */
.drone-body {
  position: relative;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 无人机中心 */
.drone-center {
  position: absolute;
  width: 16px;
  height: 16px;
  background: linear-gradient(45deg, #ff4500, #ff6b35);
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(255, 69, 0, 0.8);
  z-index: 10;
}

.drone-led {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: #fff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: led-blink 1s infinite;
}

@keyframes led-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* 无人机臂 */
.drone-arm {
  position: absolute;
  width: 20px;
  height: 3px;
  background: linear-gradient(90deg, #333, #666);
  border-radius: 2px;
  transform-origin: center;
}

.drone-arm-1 {
  top: 8px;
  left: 8px;
  transform: rotate(45deg);
}

.drone-arm-2 {
  top: 8px;
  right: 8px;
  transform: rotate(-45deg);
}

.drone-arm-3 {
  bottom: 8px;
  left: 8px;
  transform: rotate(-45deg);
}

.drone-arm-4 {
  bottom: 8px;
  right: 8px;
  transform: rotate(45deg);
}

/* 螺旋桨 */
.drone-propeller {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid #ff4500;
  border-radius: 50%;
  background: rgba(255, 69, 0, 0.1);
  animation: propeller-spin 0.1s linear infinite;
}

.drone-arm-1 .drone-propeller {
  top: -6px;
  right: -6px;
}

.drone-arm-2 .drone-propeller {
  top: -6px;
  left: -6px;
}

.drone-arm-3 .drone-propeller {
  bottom: -6px;
  right: -6px;
}

.drone-arm-4 .drone-propeller {
  bottom: -6px;
  left: -6px;
}

@keyframes propeller-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 无人机标签 */
.drone-label {
  margin-top: 8px;
  padding: 2px 8px;
  background: rgba(255, 69, 0, 0.9);
  color: white;
  font-size: 10px;
  font-weight: bold;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.drone-status {
  margin-top: 2px;
  padding: 1px 6px;
  background: rgba(0, 255, 0, 0.8);
  color: white;
  font-size: 8px;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}




/* 无人机控制按钮 - 半透明风格 */
.drone-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 1000;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.drone-control-btn {
  width: 56px;
  height: 56px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.drone-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.drone-control-btn.active {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow:
    0 0 20px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.drone-control-btn.emergency {
  border-color: rgba(255, 107, 71, 0.4);
}

.drone-control-btn.emergency:hover {
  background: rgba(255, 107, 71, 0.1);
  border-color: rgba(255, 107, 71, 0.6);
  box-shadow:
    0 6px 20px rgba(255, 107, 71, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.drone-control-btn.emergency.active {
  background: rgba(255, 107, 71, 0.2);
  border-color: rgba(255, 107, 71, 0.8);
  box-shadow:
    0 0 20px rgba(255, 107, 71, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.drone-control-btn i {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.drone-control-btn.emergency i {
  color: rgba(255, 107, 71, 1);
}

.btn-label {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-align: center;
}

/* 响应式设计 - 控制按钮 */
@media (max-width: 768px) {
  .drone-controls {
    bottom: 15px;
    gap: 6px;
    padding: 8px;
  }
  
  .drone-control-btn {
    width: 48px;
    height: 48px;
  }
  
  .drone-control-btn i {
    font-size: 16px;
    margin-bottom: 1px;
  }
  
  .btn-label {
    font-size: 8px;
  }
}

@media (max-width: 480px) {
  .drone-controls {
    bottom: 10px;
    gap: 4px;
    padding: 6px;
  }
  
  .drone-control-btn {
    width: 42px;
    height: 42px;
  }
  
  .drone-control-btn i {
    font-size: 14px;
    margin-bottom: 1px;
  }
  
  .btn-label {
    font-size: 7px;
  }
}

/* 地图控制按钮 - 半透明风格 */
.map-controls {
  position: absolute;
  top: 90px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
}

.map-control-btn {
  width: 44px;
  height: 44px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  box-shadow:
    0 2px 10px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.map-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.map-control-btn:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.05);
}

.map-control-btn i {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
}



/* 状态提示 */
.status-toast {
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(15, 25, 40, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1001;
  animation: toast-slide-in 0.3s ease;
}

@keyframes toast-slide-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-15px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 航点编辑控制按钮 */
.waypoint-controls {
  position: absolute;
  top: 20px;
  right: 84px; /* 在地图控制按钮左侧 */
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
}

.waypoint-control-btn {
  width: 44px;
  height: 44px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.waypoint-control-btn:hover:not([disabled]) {
  background: rgba(0, 150, 255, 0.2);
  border-color: rgba(0, 150, 255, 0.4);
  transform: scale(1.05);
}

.waypoint-control-btn.active {
  background: rgba(0, 150, 255, 0.3);
  border-color: rgba(0, 150, 255, 0.6);
  box-shadow: 0 0 15px rgba(0, 150, 255, 0.3);
}

.waypoint-control-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.waypoint-control-btn i {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
}

.waypoint-control-btn.active i {
  color: #00aaff;
}

/* 航点标记样式 */
.waypoint-marker {
  background: transparent !important;
  border: none !important;
}

.waypoint-icon {
  width: 30px;
  height: 30px;
  background: rgba(0, 170, 255, 0.9);
  border: 2px solid #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.waypoint-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 170, 255, 0.4);
}

.waypoint-icon.selected {
  background: rgba(255, 165, 0, 0.9);
  border-color: #ff6600;
  box-shadow: 0 0 15px rgba(255, 165, 0, 0.6);
  animation: waypoint-pulse 2s infinite;
}

.waypoint-number {
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 路径箭头样式 */
.path-arrow {
  background: transparent !important;
  border: none !important;
}

.arrow-icon {
  color: #00aaff;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 航点脉冲动画 */
@keyframes waypoint-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .flight-hud {
    min-width: 600px;
    max-width: 90%;
  }

  .waypoint-controls {
    right: 74px;
  }
}

@media (max-width: 768px) {
  .flight-hud {
    min-width: 400px;
    max-width: 95%;
    height: 45px;
    padding: 0 15px;
  }

  .hud-left, .hud-center, .hud-right {
    font-size: 11px;
  }

  .map-controls {
    top: 80px;
  }
}

@media (max-width: 480px) {
  .flight-hud {
    min-width: 320px;
    max-width: 98%;
    height: 40px;
    padding: 0 10px;
  }

  .hud-left, .hud-center, .hud-right {
    font-size: 10px;
  }
}

/* 图层选择弹窗样式 */
.layer-panel {
  position: absolute;
  top: 200px; /* 图层按钮下方，考虑4个按钮的高度 */
  right: 84px; /* 按钮右边距20px + 按钮宽度44px + 间距20px */
  width: 320px;
  max-height: 470px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  z-index: 2000;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: layer-panel-slide-in 0.3s ease-out;
  pointer-events: auto;
}

/* 弹窗箭头指向按钮 */
.layer-panel::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 30px; /* 对齐图层按钮中心 */
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid rgba(0, 0, 0, 0.6);
}

.layer-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 18px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  margin: 0 -12px 0 0;
  border-radius: 12px 12px 0 0;
  min-height: 30px;
}

.layer-panel-header i:first-child {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  margin-right: 12px;
}

.layer-panel-header h3 {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  flex: 1;
  display: flex;
  align-items: center;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #ff6b6b;
}

.layer-panel-content {
  padding: 10px 18px 30px 18px;
  max-height: 480px;
  overflow-y: auto;
}

.layer-section {
  margin-bottom: 20px;
}

.layer-section:last-child {
  margin-bottom: 10px;
}

.layer-section h4 {
  margin: 0 0 12px 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.layer-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-option {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.layer-option:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.layer-option.active {
  background: rgba(0, 150, 255, 0.15);
  border-color: rgba(0, 150, 255, 0.4);
}

.layer-option i {
  font-size: 18px;
  color: #00aaff;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.layer-info {
  flex: 1;
}

.layer-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.layer-desc {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  line-height: 1.3;
}

.overlay-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.overlay-option {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.overlay-option:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.overlay-toggle {
  display: flex;
  align-items: center;
  padding: 10px 15px;
}

.overlay-toggle input[type="checkbox"] {
  margin-right: 12px;
  width: 16px;
  height: 16px;
  accent-color: #00aaff;
}

.overlay-toggle i {
  font-size: 16px;
  color: #00aaff;
  margin-right: 12px;
  width: 18px;
  text-align: center;
}

.overlay-info {
  flex: 1;
}

.overlay-name {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.overlay-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.4;
}

/* 弹窗动画 */
@keyframes layer-panel-slide-in {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layer-panel {
    top: 180px;
    right: 20px;
    left: 20px;
    width: auto;
    max-width: none;
  }

  .layer-panel::before {
    right: 42px; /* 调整箭头位置对齐按钮 */
  }
}

@media (max-width: 480px) {
  .layer-panel {
    top: 170px;
    right: 10px;
    left: 10px;
    width: auto;
    max-width: none;
  }

  .layer-panel::before {
    right: 32px; /* 调整箭头位置对齐按钮 */
  }

  .layer-panel-header {
    padding: 18px 18px;
    min-height: 50px;
  }

  .layer-panel-content {
    padding: 0 18px 30px 18px;
  }
}


</style>